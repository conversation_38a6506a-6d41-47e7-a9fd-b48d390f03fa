/**
 * Lullaby Clinic - Translations
 * Multi-language content for the website
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

export interface Translation {
  // Navigation
  nav: {
    home: string;
    services: string;
    about: string;
    gallery: string;
    blog: string;
    bookNow: string;
    login: string;
    signup: string;
    dashboard: string;
    logout: string;
  };

  // Authentication
  auth: {
    login: string;
    signup: string;
    logout: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone: string;
    loginSuccess: string;
    loginFailed: string;
    signupSuccess: string;
    signupFailed: string;
    processingLogin: string;
    pleaseWait: string;
    verifyingCredentials: string;
    redirectingToDashboard: string;
    loginSuccessMessage: string;
    loginErrorOccurred: string;
    tryAgain: string;
    socialAuthError: string;
    socialAuthErrorDescription: string;
    socialLoginSuccess: string;
    noSessionError: string;
    unexpectedError: string;
    continueWithGoogle: string;
    continueWithFacebook: string;
    signupWithGoogle: string;
    signupWithFacebook: string;
  };

  // Hero
  hero: {
    badge: string;
    title: string;
    subtitle: string;
    bookingTitle: string;
    selectService: string;
    selectDoctor: string;
    selectBranch: string;
    bookButton: string;
    happyClients: string;
    yearsExperience: string;
    bookingModal: {
      title: string;
      description: string;
      summary: string;
      confirm: string;
    };
  };

  // Services
  services: {
    badge: string;
    title: string;
    subtitle: string;
    popular: string;
    starting: string;
    learnMore: string;
    viewAll: string;
    facial: {
      title: string;
      description: string;
      price: string;
    };
    botox: {
      title: string;
      description: string;
      price: string;
    };
    laser: {
      title: string;
      description: string;
      price: string;
    };
  };

  // Testimonials
  testimonials: {
    badge: string;
    title: string;
    subtitle: string;
    testimonial1: {
      name: string;
      message: string;
      service: string;
    };
    testimonial2: {
      name: string;
      message: string;
      service: string;
    };
    testimonial3: {
      name: string;
      message: string;
      service: string;
    };
  };

  // Gallery
  gallery: {
    badge: string;
    title: string;
    subtitle: string;
    before: string;
    after: string;
    viewMore: string;
    item1: {
      treatment: string;
      description: string;
    };
    item2: {
      treatment: string;
      description: string;
    };
    item3: {
      treatment: string;
      description: string;
    };
    item4: {
      treatment: string;
      description: string;
    };
    item5: {
      treatment: string;
      description: string;
    };
    item6: {
      treatment: string;
      description: string;
    };
  };

  // Blog
  blog: {
    badge: string;
    title: string;
    subtitle: string;
    readMore: string;
    viewAll: string;
    post1: {
      title: string;
      excerpt: string;
      date: string;
      readTime: string;
      category: string;
    };
    post2: {
      title: string;
      excerpt: string;
      date: string;
      readTime: string;
      category: string;
    };
    post3: {
      title: string;
      excerpt: string;
      date: string;
      readTime: string;
      category: string;
    };
  };

  // Footer
  footer: {
    partners: string;
    description: string;
    address: string;
    phone: string;
    email: string;
    quickLinks: string;
    services: string;
    rights: string;
    socialMedia: {
      facebook: string;
      line: string;
      googleMaps: string;
    };
  };

  // Flash Sale
  flashSale: {
    title: string;
    subtitle: string;
    discount: string;
    timeLeft: string;
    days: string;
    hours: string;
    minutes: string;
    seconds: string;
    bookNow: string;
    slotsLeft: string;
    todayOnly: string;
    limitedSpots: string;
    lastChance: string;
    popularTreatment: string;
    realTimeBookings: string;
    justBooked: string;
  };

  // Service Detail
  serviceDetail: {
    backToServices: string;
    bookNow: string;
    callNow: string;
    whatsApp: string;
    overview: string;
    beforeAfter: string;
    testimonials: string;
    faq: string;
    procedure: string;
    aftercare: string;
    benefits: string;
    duration: string;
    price: string;
    originalPrice: string;
    discountedPrice: string;
    popularityScore: string;
    availableSlots: string;
    limitedAvailability: string;
    almostFullyBooked: string;
    highDemand: string;
    verified: string;
    recommended: string;
    todaySpecial: string;
    bookingUrgency: string;
    peopleViewing: string;
    lastBooking: string;
    satisfaction: string;
    safetyFirst: string;
    expertCare: string;
    premiumQuality: string;
  };

  // Blog Search
  blogSearch: {
    searchPlaceholder: string;
    filterBy: string;
    categories: string;
    allCategories: string;
    sortBy: string;
    newest: string;
    oldest: string;
    mostPopular: string;
    mostViewed: string;
    readTime: string;
    author: string;
    tags: string;
    featured: string;
    trending: string;
    results: string;
    noResults: string;
    noResultsDesc: string;
    clearFilters: string;
    readMore: string;
    minutes: string;
    views: string;
    likes: string;
    difficulty: {
      beginner: string;
      intermediate: string;
      advanced: string;
    };
  };

  // Newsletter
  newsletter: {
    title: string;
    subtitle: string;
    emailPlaceholder: string;
    subscribe: string;
    subscribing: string;
    successTitle: string;
    successMessage: string;
    errorMessage: string;
    privacyNotice: string;
    exclusiveOffers: string;
    limitedTime: string;
    subscribersOnly: string;
    benefits: {
      earlyAccess: string;
      exclusiveDiscounts: string;
      expertTips: string;
      monthlyOffers: string;
      freeConsultation: string;
      priorityBooking: string;
    };
    incentives: {
      discount: string;
      freeConsult: string;
      earlyAccess: string;
      vipTreatment: string;
    };
    urgency: {
      limitedSpots: string;
      joinToday: string;
      spotsLeft: string;
      memberBenefit: string;
      timeLeft: string;
      hours: string;
      minutes: string;
    };
    socialProof: {
      membersJoined: string;
      thisWeek: string;
      satisfaction: string;
      rating: string;
    };
  };

  // Promotions
  promotions: {
    title: string;
    subtitle: string;
    monthlySpecial: string;
    limitedTime: string;
    bookNow: string;
    learnMore: string;
    save: string;
    originalPrice: string;
    newPrice: string;
    validUntil: string;
    spotsLeft: string;
    almostGone: string;
    lastChance: string;
    popular: string;
    trending: string;
    newOffer: string;
    featured: string;
    timeLeft: string;
    days: string;
    hours: string;
    minutes: string;
    seconds: string;
    benefits: string;
    terms: string;
    viewAll: string;
    bookingProgress: string;
    peopleBooked: string;
    thisMonth: string;
    exclusiveOffer: string;
    memberOnly: string;
  };

  // About Page
  about: {
    hero: {
      title: string;
      subtitle: string;
    };
    mission: {
      title: string;
      description: string;
    };
    vision: {
      title: string;
      description: string;
    };
    approach: {
      title: string;
      description: string;
    };
    doctors: {
      title: string;
      team: {
        dr_smith: {
          name: string;
          role: string;
        };
        dr_lee: {
          name: string;
          role: string;
        };
        dr_chen: {
          name: string;
          role: string;
        };
      };
    };
    partners: {
      title: string;
    };
    brand: {
      statement: string;
    };
    quickLinks: {
      title: string;
    };
    servicesSection: {
      title: string;
      learnMore: string;
    };
  };

  // Services Page
  servicesPage: {
    hero: {
      title: string;
      subtitle: string;
    };
    treatments: {
      title: string;
      facial: {
        title: string;
        description: string;
        duration: string;
        price: string;
        benefits: string[];
      };
      botox: {
        title: string;
        description: string;
        duration: string;
        price: string;
        benefits: string[];
      };
      laser: {
        title: string;
        description: string;
        duration: string;
        price: string;
        benefits: string[];
      };
      filler: {
        title: string;
        description: string;
        duration: string;
        price: string;
        benefits: string[];
      };
    };
    booking: {
      title: string;
      subtitle: string;
      button: string;
    };
  };

  // Doctors Page
  doctorsPage: {
    hero: {
      title: string;
      subtitle: string;
    };
    team: {
      dr_smith: {
        name: string;
        role: string;
        specialties: string[];
        experience: string;
        education: string[];
        description: string;
      };
      dr_lee: {
        name: string;
        role: string;
        specialties: string[];
        experience: string;
        education: string[];
        description: string;
      };
      dr_chen: {
        name: string;
        role: string;
        specialties: string[];
        experience: string;
        education: string[];
        description: string;
      };
    };
    appointment: {
      title: string;
      button: string;
    };
  };

  // Contact Page
  contactPage: {
    hero: {
      title: string;
      subtitle: string;
    };
    info: {
      address: {
        title: string;
        value: string;
      };
      phone: {
        title: string;
        value: string;
      };
      email: {
        title: string;
        value: string;
      };
      hours: {
        title: string;
        value: string;
      };
    };
    form: {
      title: string;
      name: string;
      email: string;
      phone: string;
      subject: string;
      message: string;
      submit: string;
      success: string;
      error: string;
    };
    map: {
      title: string;
    };
  };

  // Blog Page Enhanced
  blogPage: {
    hero: {
      title: string;
      subtitle: string;
    };
    search: {
      placeholder: string;
      button: string;
    };
    categories: {
      all: string;
      skincare: string;
      treatments: string;
      tips: string;
      news: string;
    };
    posts: {
      skincare: {
        title: string;
        excerpt: string;
        date: string;
      };
      botox: {
        title: string;
        excerpt: string;
        date: string;
      };
      laser: {
        title: string;
        excerpt: string;
        date: string;
      };
      wintercare: {
        title: string;
        excerpt: string;
        date: string;
      };
      antiaging: {
        title: string;
        excerpt: string;
        date: string;
      };
      consultation: {
        title: string;
        excerpt: string;
        date: string;
      };
    };
    readMore: string;
    readTime: string;
    author: string;
    tags: string;
    pagination: {
      previous: string;
      next: string;
    };
  };

  // Common
  common: {
    next: string;
    previous: string;
    and: string;
    loading: string;
    error: string;
    retry: string;
    bookNow: string;
    learnMore: string;
    contactUs: string;
    viewAll: string;
  };
}

export const translations: Record<string, Translation> = {
  th: {
    openHours: "เปิด จ-ส 9:00-18:00",
    nav: {
      home: 'หน้าหลัก',
      services: 'บริการ',
      about: 'เกี่ยวกับเรา',
      gallery: 'ผลงาน',
      blog: 'บทความ',
      bookNow: 'จองนัด',
      login: 'เข้าสู่ระบบ',
      signup: 'สมัครสมาชิก',
      dashboard: 'แดชบอร์ด',
      logout: 'ออกจากระบบ',
    },

    auth: {
      login: 'เข้าสู่ระบบ',
      signup: 'สมัครสมาชิก',
      logout: 'ออกจากระบบ',
      email: 'อีเมล',
      password: 'รหัสผ่าน',
      firstName: 'ชื่อ',
      lastName: 'นามสกุล',
      phone: 'เบอร์โทรศัพท์',
      loginSuccess: 'เข้าสู่ระบบสำเร็จ',
      loginFailed: 'เข้าสู่ระบบไม่สำเร็จ',
      signupSuccess: 'สมัครสมาชิกสำเร็จ',
      signupFailed: 'สมัครสมาชิกไม่สำเร็จ',
      processingLogin: 'กำลังดำเนินการเข้าสู่ระบบ',
      pleaseWait: 'กรุณารอสักครู่',
      verifyingCredentials: 'กำลังตรวจสอบข้อมูล',
      redirectingToDashboard: 'กำลังเปลี่ยนหน้าไปยังแดชบอร์ด',
      loginSuccessMessage: 'คุณได้เข้าสู่ระบบเรียบร้อยแล้ว',
      loginErrorOccurred: 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ',
      tryAgain: 'ลองใหม่อีกครั้ง',
      socialAuthError: 'ข้อผิดพลาดในการเข้าสู่ระบบ',
      socialAuthErrorDescription: 'ไม่สามารถเข้าสู่ระบบผ่านโซเชียลมีเดียได้',
      socialLoginSuccess: 'เข้าสู่ระบบผ่านโซเชียลมีเดียสำเร็จ',
      noSessionError: 'ไม่พบเซสชันผู้ใช้',
      unexpectedError: 'เกิดข้อผิดพลาดที่ไม่คาดคิด',
      continueWithGoogle: 'ดำเนินการต่อด้วย Google',
      continueWithFacebook: 'ดำเนินการต่อด้วย Facebook',
      signupWithGoogle: 'สมัครสมาชิกด้วย Google',
      signupWithFacebook: 'สมัครสมาชิกด้วย Facebook',
    },

    hero: {
      badge: "คลินิกความงามชั้นนำ",
      title: "เปลี่ยนความฝัน ให้เป็นจริง",
      subtitle: "ด้วยเทคโนโลยีล่าสุดและทีมแพทย์ผู้เชี่ยวชาญ เราพร้อมมอบการดูแลความงามที่ดีที่สุดให้กับคุณ",
      bookingTitle: "จองนัดหมาย",
      selectService: "เลือกบริการ",
      selectDoctor: "เลือกแพทย์",
      selectBranch: "เลือกสาขา",
      bookButton: "จองนัดหมาย",
      happyClients: "ลูกค้าพอใจ",
      yearsExperience: "ปีประสบการณ์",
      bookingModal: {
        title: "ยืนยันการจองนัดหมาย",
        description: "กรุณาตรวจสอบข้อมูลการจองของคุณ",
        summary: "สรุปการจอง",
        confirm: "ยืนยันการจอง"
      }
    },
    services: {
      badge: "บริการของเรา",
      title: "บริการความงามครบวงจร",
      subtitle: "เราให้บริการด้วยเทคโนโลยีล่าสุดและทีมแพทย์ผู้เชี่ยวชาญ",
      popular: "ยอดนิยม",
      starting: "เริ่มต้น",
      learnMore: "เรียนรู้เพิ่มเติม",
      viewAll: "ดูทั้งหมด",
      facial: {
        title: "ฟื้นฟูผิวหน้า",
        description: "ดูแลผิวหน้าด้วยเทคโนโลยีล่าสุด เพื่อผิวที่กระจ่างใส",
        price: "฿2,500"
      },
      botox: {
        title: "โบท็อกซ์",
        description: "ลดริ้วรอย ยกกระชับหน้า ด้วยโบท็อกซ์คุณภาพสูง",
        price: "฿8,000"
      },
      laser: {
        title: "เลเซอร์ผิว",
        description: "กำจัดจุดด่างดำ ฝ้า กระ ด้วยเลเซอร์ล่าสุด",
        price: "฿5,000"
      }
    },
    testimonials: {
      badge: "ความคิดเห็น",
      title: "เสียงจากลูกค้า",
      subtitle: "ฟังประสบการณ์จริงจากลูกค้าที่เลือกใช้บริการกับเรา",
      testimonial1: {
        name: "คุณอรอำพร",
        message: "พอใจมากค่ะ ผิวหน้าขาวขึ้น กระก็จางลง แพทย์ให้คำปรึกษาดีมาก",
        service: "ฟื้นฟูผิวหน้า"
      },
      testimonial2: {
        name: "คุณสมศรี",
        message: "โบท็อกซ์ที่นี่ธรรมชาติมาก หน้าเรียบเนียน ไม่แข็ง ประทับใจค่ะ",
        service: "โบท็อกซ์"
      },
      testimonial3: {
        name: "คุณมาลี",
        message: "เลเซอร์กำจัดฝ้าได้ผลดีมาก หลังทำเสร็จผิวใสขึ้นเยอะเลย",
        service: "เลเซอร์ผิว"
      }
    },
    gallery: {
      badge: "ผลงาน",
      title: "ผลงานก่อน-หลัง",
      subtitle: "ชมผลงานการรักษาที่ได้ผลจริง จากลูกค้าของเรา",
      before: "ก่อน",
      after: "หลัง",
      viewMore: "ดูเพิ่มเติม",
      item1: {
        treatment: "ฟื้นฟูผิวหน้า",
        description: "ผิวกระจ่างใส ลดริ้วรอย"
      },
      item2: {
        treatment: "โบท็อกซ์",
        description: "ลดริ้วรอยรอบดวงตา"
      },
      item3: {
        treatment: "เลเซอร์",
        description: "กำจัดฝ้ากระ"
      },
      item4: {
        treatment: "ฟิลเลอร์",
        description: "เติมเต็มร่องแก้ม"
      },
      item5: {
        treatment: "ยกกระชับ",
        description: "ยกกระชับหน้า"
      },
      item6: {
        treatment: "ฉีดผิวขาว",
        description: "ผิวขาวใสทั้งหน้า"
      }
    },
    blog: {
      badge: "บทความ",
      title: "บทความความงาม",
      subtitle: "อัพเดตเทรนด์ความงามและเทคนิคการดูแลผิวใหม่ล่าสุด",
      readMore: "อ่านต่อ",
      viewAll: "ดูทั้งหมด",
      post1: {
        title: "5 วิธีดูแลผิวหน้าให้ขาวใส",
        excerpt: "เคล็ดลับการดูแลผิวหน้าให้ขาวใสธรรมชาติ ด้วยวิธีง่ายๆ ที่ทำได้เอง",
        date: "15 ม.ค. 2024",
        readTime: "5 นาที",
        category: "สกินแคร์"
      },
      post2: {
        title: "โบท็อกซ์ vs ฟิลเลอร์ ต่างกันอย่างไร",
        excerpt: "ทำความเข้าใจความแตกต่างระหว่างโบท็อกซ์และฟิลเลอร์ เลือกใช้ให้ถูกวิธี",
        date: "10 ม.ค. 2024",
        readTime: "7 นาที",
        category: "การรักษา"
      },
      post3: {
        title: "การดูแลหลังฉีดโบท็อกซ์",
        excerpt: "คำแนะนำการดูแลตนเองหลังฉีดโบท็อกซ์ เพื่อผลลัพธ์ที่ดีที่สุด",
        date: "5 ม.ค. 2024",
        readTime: "4 นาที",
        category: "หลังการรักษา"
      }
    },
    footer: {
      partners: "พาร์ทเนอร์แบรนด์ชั้นนำ",
      description: "Lullaby Clinic คลินิกความงามที่ให้บริการด้วยเทคโนโลยีล่าสุดและทีมแพทย์ผู้เชี่ยวชาญ",
      address: "170 25 หมู่3 ตำบลเสม็ด ถนนพระยาสัจจา จ.ชลบุรี 20000",
      phone: "************",
      email: "<EMAIL>",
      quickLinks: "ลิงก์ด่วน",
      services: "บริการ",
      rights: "สงวนลิขสิทธิ์",
      socialMedia: {
        facebook: "https://www.facebook.com/Lullabyclinic",
        line: "@Lullabyclinic",
        googleMaps: "https://g.co/kgs/VgRu8jW"
      }
    },
    flashSale: {
      title: "โปรโมชั่นแฟลชเซล",
      subtitle: "วันนี้เท่านั้น - เฉพาะ 24 ชั่วโมง",
      discount: "ลด 40%",
      timeLeft: "เวลาที่เหลือ",
      days: "วัน",
      hours: "ชั่วโมง",
      minutes: "นาที",
      seconds: "วินาที",
      bookNow: "จองเลย",
      slotsLeft: "ที่นั่งที่เหลือ",
      todayOnly: "วันนี้เท่านั้น",
      limitedSpots: "ที่นั่งจำกัด",
      lastChance: "โอกาสสุดท้าย",
      popularTreatment: "ทรีทเมนต์ยอดนิยม",
      realTimeBookings: "การจองแบบเรียลไทม์",
      justBooked: "เพิ่งจองไป"
    },
    serviceDetail: {
      backToServices: "กลับไปยังบริการ",
      bookNow: "จองเลย",
      callNow: "โทรเลย",
      whatsApp: "WhatsApp",
      overview: "ภาพรวม",
      beforeAfter: "ก่อน/หลัง",
      testimonials: "รีวิว",
      faq: "คำถามที่พบบ่อย",
      procedure: "ขั้นตอนการรักษา",
      aftercare: "การดูแลหลังรักษา",
      benefits: "ประโยชน์",
      duration: "ระยะเวลา",
      price: "ราคา",
      originalPrice: "ราคาเดิม",
      discountedPrice: "ราคาลด",
      popularityScore: "คะแนนความนิยม",
      availableSlots: "ที่ว่างที่มี",
      limitedAvailability: "ที่ว่างจำกัด",
      almostFullyBooked: "เกือบเต็มแล้ว",
      highDemand: "ความต้องการสูง",
      verified: "ยืนยันแล้ว",
      recommended: "แนะนำ",
      todaySpecial: "พิเศษวันนี้",
      bookingUrgency: "การจองด่วน",
      peopleViewing: "คนกำลังดู",
      lastBooking: "การจองล่าสุด",
      satisfaction: "ความพึงพอใจ",
      safetyFirst: "ความปลอดภัยเป็นอันดับหนึ่ง",
      expertCare: "การดูแลจากผู้เชี่ยวชาญ",
      premiumQuality: "คุณภาพพรีเมียม"
    },
    blogSearch: {
      searchPlaceholder: "ค้นหาบทความ, แท็ก หรือคำหลัก...",
      filterBy: "กรองตาม",
      categories: "หมวดหมู่",
      allCategories: "ทุกหมวดหมู่",
      sortBy: "เรียงตาม",
      newest: "ใหม่ที่สุด",
      oldest: "เก่าที่สุด",
      mostPopular: "ยอดนิยมมากที่สุด",
      mostViewed: "ดูมากที่สุด",
      readTime: "เวลาอ่าน",
      author: "ผู้เขียน",
      tags: "แท็ก",
      featured: "แนะนำ",
      trending: "กำลังฮิต",
      results: "ผลการค้นหา",
      noResults: "ไม่พบผลลัพธ์",
      noResultsDesc: "ลองค้นหาด้วยคำอื่น หรือลบตัวกรอง",
      clearFilters: "ล้างตัวกรอง",
      readMore: "อ่านต่อ",
      minutes: "นาที",
      views: "ครั้งที่ดู",
      likes: "ใจ",
      difficulty: {
        beginner: "เริ่มต้น",
        intermediate: "กลาง",
        advanced: "ขั้นสูง"
      }
    },
    newsletter: {
      title: "รับข้อเสนอพิเศษ",
      subtitle: "สมัครรับจดหมายข่าวเพื่อรับข้อเสนอล่าสุดและเคล็ดลับความงาม",
      emailPlaceholder: "กรอกอีเมลของคุณ",
      subscribe: "สมัครเลย",
      subscribing: "กำลังสมัคร...",
      successTitle: "สมัครสำเร็จ!",
      successMessage: "ขอบคุณสำหรับการสมัคร! ตรวจสอบอีเมลของคุณสำหรับข้อเสนอพิเศษ",
      errorMessage: "เกิดข้อผิดพลาด กรุณาลองใหม่",
      privacyNotice: "ฉันยินยอมรับอีเมลการตลาดและได้อ่านนโยบายความเป็นส่วนตัวแล้ว",
      exclusiveOffers: "ข้อเสนอพิเศษ",
      limitedTime: "เวลาจำกัด",
      subscribersOnly: "สำหรับสมาชิกเท่านั้น",
      benefits: {
        earlyAccess: "เข้าถึงทรีทเมนต์ใหม่ก่อนใคร",
        exclusiveDiscounts: "ส่วนลดพิเศษ",
        expertTips: "เคล็ดลับจากผู้เชี่ยวชาญ",
        monthlyOffers: "ข้อเสนอรายเดือน",
        freeConsultation: "การปรึกษาฟรี",
        priorityBooking: "การจองแบบมีความสำคัญ"
      },
      incentives: {
        discount: "ลด 20%",
        freeConsult: "ปรึกษาฟรี",
        earlyAccess: "เข้าถึงก่อน",
        vipTreatment: "การรักษา VIP"
      },
      urgency: {
        limitedSpots: "ที่นั่งจำกัด",
        joinToday: "เข้าร่วมวันนี้",
        spotsLeft: "ที่เหลือ",
        memberBenefit: "สิทธิประโยชน์สมาชิก",
        timeLeft: "เวลาที่เหลือ",
        hours: "ชั่วโมง",
        minutes: "นาที"
      },
      socialProof: {
        membersJoined: "สมาชิกเข้าร่วม",
        thisWeek: "สัปดาห์นี้",
        satisfaction: "ความพึงพอใจ",
        rating: "คะแนน"
      }
    },
    promotions: {
      title: "โปรโมชั่นรายเดือน",
      subtitle: "ค้นพบข้อเสนอพิเศษที่คัดสรรมาเป็นพิเศษ รับราคาสุดคุ้มเป็นเวลาจำกัด",
      monthlySpecial: "พิเศษประจำเดือน",
      limitedTime: "เวลาจำกัด",
      bookNow: "จองเลย",
      learnMore: "เรียนรู้เพิ่มเติม",
      save: "ประหยัด",
      originalPrice: "ราคาเดิม",
      newPrice: "ราคาใหม่",
      validUntil: "ใช้ได้ถึง",
      spotsLeft: "ที่เหลือ",
      almostGone: "เกือบหมดแล้ว",
      lastChance: "โอกาสสุดท้าย",
      popular: "ยอดนิยม",
      trending: "กำลังฮิต",
      newOffer: "ข้อเสนอใหม่",
      featured: "แนะนำ",
      timeLeft: "เวลาที่เหลือ",
      days: "วัน",
      hours: "ชั่วโมง",
      minutes: "นาที",
      seconds: "วินาที",
      benefits: "สิทธิประโยชน์",
      terms: "เงื่อนไข",
      viewAll: "ดูทั้งหมด",
      bookingProgress: "ความคืบหน้าการจอง",
      peopleBooked: "คนจองแล้ว",
      thisMonth: "เดือนนี้",
      exclusiveOffer: "ข้อเสนอพิเศษ",
      memberOnly: "สำหรับสมาชิกเท่านั้น"
    },
    form: {
      email: "อีเมล",
      password: "รหัสผ่าน",
      confirmPassword: "ยืนยันรหัสผ่าน",
      firstName: "ชื่อ",
      lastName: "นามสกุล",
      phone: "เบอร์โทรศัพท์",
      dateOfBirth: "วันเกิด",
      gender: "เพศ",
      emailPlaceholder: "กรุณากรอกอีเมลของคุณ",
      passwordPlaceholder: "กรุณากรอกรหัสผ่านของคุณ",
      confirmPasswordPlaceholder: "กรุณายืนยันรหัสผ่าน",
      firstNamePlaceholder: "กรุณากรอกชื่อ",
      lastNamePlaceholder: "กรุณากรอกนามสกุล",
      phonePlaceholder: "กรุณากรอกเบอร์โทรศัพท์",
      selectGender: "เลือกเพศ",
      male: "ชาย",
      female: "หญิง",
      other: "อื่นๆ",
      preferNotToSay: "ไม่ระบุ"
    },
    validation: {
      emailRequired: "กรุณากรอกอีเมล",
      emailInvalid: "รูปแบบอีเมลไม่ถูกต้อง",
      passwordRequired: "กรุณากรอกรหัสผ่าน",
      passwordTooShort: "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร",
      passwordWeak: "รหัสผ่านต้องประกอบด้วยตัวพิมพ์เล็ก พิมพ์ใหญ่ และตัวเลข",
      confirmPasswordRequired: "กรุณายืนยันรหัสผ่าน",
      passwordMismatch: "รหัสผ่านไม่ตรงกัน",
      firstNameRequired: "กรุณากรอกชื่อ",
      lastNameRequired: "กรุณากรอกนามสกุล",
      phoneRequired: "กรุณากรอกเบอร์โทรศัพท์",
      phoneInvalid: "รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง",
      privacyConsentRequired: "กรุณายอมรับเงื่อนไขการใช้งาน"
    },
    about: {
      hero: {
        title: "เกี่ยวกับ Lullaby Clinic",
        subtitle: "ผสมผสานเทคโนโลยีการแพทย์เสริมความงามสมัยใหม่กับการดูแลที่เต็มไปด้วยความใส่ใจ เพื่อช่วยให้คุณมั่นใจในตัวเอง"
      },
      mission: {
        title: "พันธกิจของเรา",
        description: "ให้บริการการรักษาความงามที่ปลอดภัยและมีประสิทธิภาพ โดยใช้เทคโนโลยีล่าสุดและแนวทางปฏิบัติที่อิงหลักฐานทางวิทยาศาสตร์"
      },
      vision: {
        title: "วิสัยทัศน์ของเรา",
        description: "เป็นคลินิกที่ได้รับความไว้วางใจมากที่สุดในการดูแลด้านสุนทรียศาสตร์แบบองค์รวมในเอเชีย กำหนดมาตรฐานใหม่ของความเป็นเลิศ"
      },
      approach: {
        title: "แนวทางของเรา",
        description: "เราเชื่อในแผนการรักษาที่เป็นส่วนบุคคล การนวัตกรรมอย่างต่อเนื่อง และการบรรลุผลลัพธ์ที่ดูเป็นธรรมชาติซึ่งช่วยเสริมความงามที่เป็นเอกลักษณ์ของคุณ ลูกค้าทุกคนจะได้รับการปรึกษาที่ครอบคลุมและการดูแลอย่างต่อเนื่องจากทีมผู้เชี่ยวชาญของเรา"
      },
      doctors: {
        title: "พบกับแพทย์ของเรา",
        team: {
          dr_smith: {
            name: "ดร. สมิท",
            role: "ผู้ก่อตั้งและผู้อำนวยการฝ่ายการแพทย์"
          },
          dr_lee: {
            name: "ดร. ลี",
            role: "แพทย์ผิวหนัง"
          },
          dr_chen: {
            name: "ดร. เฉิน",
            role: "แพทย์เสริมความงาม"
          }
        }
      },
      partners: {
        title: "พาร์ทเนอร์ที่เชื่อถือได้ของเรา"
      },
      brand: {
        statement: "เปลี่ยนแปลงการเดินทางความงามของคุณด้วยการรักษาระดับโลกและการดูแลจากผู้เชี่ยวชาญของเรา"
      },
      quickLinks: {
        title: "ลิงก์ด่วน"
      },
      servicesSection: {
        title: "บริการ",
        learnMore: "เรียนรู้เพิ่มเติม"
      }
    },
    servicesPage: {
      hero: {
        title: "บริการของเรา",
        subtitle: "ค้นพบการรักษาความงามที่ครอบคลุมด้วยเทคโนโลยีล่าสุดและทีมแพทย์ผู้เชี่ยวชาญ"
      },
      treatments: {
        title: "การรักษาของเรา",
        facial: {
          title: "ฟื้นฟูผิวหน้าพรีเมียม",
          description: "การรักษาผิวหน้าขั้นสูงด้วยผลิตภัณฑ์คุณภาพทางการแพทย์เพื่อผิวที่กระจ่างใสและอ่อนเยาว์",
          duration: "60-90 นาที",
          price: "฿2,500-4,500",
          benefits: ["ลดริ้วรอยและจุดด่างดำ", "เพิ่มความชุ่มชื้นและความยืดหยุ่น", "กระชับรูขุมขน", "ผิวกระจ่างใสทันที"]
        },
        botox: {
          title: "โบท็อกซ์",
          description: "ลดริ้วรอยและยกกระชับใบหน้าด้วยโบท็อกซ์คุณภาพสูงจากแพทย์ผู้เชี่ยวชาญ",
          duration: "30-45 นาที",
          price: "฿8,000-15,000",
          benefits: ["ลดริ้วรอยหน้าผาก", "ยกกระชับคิ้วและตา", "ลดริ้วรอยรอบดวงตา", "ผลลัพธ์ธรรมชาติ"]
        },
        laser: {
          title: "เลเซอร์ผิว",
          description: "เทคโนโลยีเลเซอร์ล่าสุดสำหรับการฟื้นฟูผิวและต่อต้านริ้วรอย",
          duration: "45-90 นาที",
          price: "฿5,000-12,000",
          benefits: ["กำจัดจุดด่างดำและฝ้า", "ลดขนาดรูขุมขน", "กระชับผิว", "ลดริ้วรอยแผลเป็น"]
        },
        filler: {
          title: "ฟิลเลอร์",
          description: "เติมเต็มและปรับรูปหน้าด้วยฟิลเลอร์คุณภาพสูงสำหรับความงามที่เป็นธรรมชาติ",
          duration: "30-60 นาที",
          price: "฿12,000-25,000",
          benefits: ["เติมเต็มร่องแก้ม", "ยกกระชับใบหน้า", "ปรับรูปจมูก", "เพิ่มความอิ่มฟูให้ริมฝีปาก"]
        }
      },
      booking: {
        title: "พร้อมเริ่มต้นการเดินทางความงามของคุณ?",
        subtitle: "จองคำปรึกษาฟรีกับแพทย์ผู้เชี่ยวชาญของเรา",
        button: "จองนัดหมาย"
      }
    },
    doctorsPage: {
      hero: {
        title: "ทีมแพทย์ผู้เชี่ยวชาญ",
        subtitle: "พบกับทีมแพทย์มืออาชีพที่มีประสบการณ์และความเชี่ยวชาญในด้านการแพทย์เสริมความงาม"
      },
      team: {
        dr_smith: {
          name: "ดร. สมิท จอห์นสัน",
          role: "ผู้ก่อตั้งและผู้อำนวยการฝ่ายการแพทย์",
          specialties: ["ศัลยกรรมตกแต่ง", "การฉีดโบท็อกซ์และฟิลเลอร์", "เลเซอร์ผิว"],
          experience: "15+ ปีประสบการณ์",
          education: ["แพทยศาสตร์บัณฑิต จุฬาลงกรณ์มหาวิทยาลัย", "อนุสาขาศัลยกรรมตกแต่ง มหาวิทยาลัยมหิดล", "เฟลโลว์ชิป เกาหลีใต้"],
          description: "ด้วยประสบการณ์กว่า 15 ปี ดร.สมิทเป็นผู้เชี่ยวชาญในด้านการแพทย์เสริมความงามและได้รับการยอมรับในระดับสากล"
        },
        dr_lee: {
          name: "ดร. ลี ซูยอน",
          role: "แพทย์ผิวหนัง",
          specialties: ["โรคผิวหนัง", "เลเซอร์ผิว", "การรักษาสิว"],
          experience: "12+ ปีประสบการณ์",
          education: ["แพทยศาสตร์บัณฑิต มหาวิทยาลัยเชียงใหม่", "อนุสาขาโรคผิวหนัง โรงพยาบาลศิริราช", "เฟลโลว์ชิป ญี่ปุ่น"],
          description: "ผู้เชี่ยวชาญด้านโรคผิวหนังและการใช้เลเซอร์ในการรักษา มีประสบการณ์ในการดูแลผู้ป่วยมากมาย"
        },
        dr_chen: {
          name: "ดร. เฉิน หวานหวาน",
          role: "แพทย์เสริมความงาม",
          specialties: ["ฟิลเลอร์", "โบท็อกซ์", "ศัลยกรรมไขมัน"],
          experience: "10+ ปีประสบการณ์",
          education: ["แพทยศาสตร์บัณฑิต มหาวิทยาลัยธรรมศาสตร์", "อนุสาขาศัลยกรรมตกแต่ง โรงพยาบาลรามาธิบดี", "เฟลโลว์ชิป สิงคโปร์"],
          description: "ผู้เชี่ยวชาญในการฉีดฟิลเลอร์และโบท็อกซ์ที่ให้ผลลัพธ์ธรรมชาติและปลอดภัย"
        }
      },
      appointment: {
        title: "จองคำปรึกษากับแพทย์",
        button: "จองนัดหมาย"
      }
    },
    contactPage: {
      hero: {
        title: "ติดต่อเรา",
        subtitle: "พร้อมให้คำปรึกษาและตอบคำถามทุกเรื่องเกี่ยวกับการรักษาความงาม"
      },
      info: {
        address: {
          title: "ที่อยู่",
          value: "170 25 หมู่3 ตำบลเสม็ด ถนนพระยาสัจจา จ.ชลบุรี 20000"
        },
        phone: {
          title: "โทรศัพท์",
          value: "************"
        },
        email: {
          title: "อีเมล",
          value: "<EMAIL>"
        },
        hours: {
          title: "เวลาเปิด-ปิด",
          value: "จันทร์-เสาร์ 9:00-18:00"
        }
      },
      form: {
        title: "ส่งข้อความถึงเรา",
        name: "ชื่อ-นามสกุล",
        email: "อีเมล",
        phone: "เบอร์โทรศัพท์",
        subject: "หัวข้อ",
        message: "ข้อความ",
        submit: "ส่งข้อความ",
        success: "ส่งข้อความสำเร็จ เราจะติดต่อกลับภายใน 24 ชั่วโมง",
        error: "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง"
      },
      map: {
        title: "แผนที่"
      }
    },
    blogPage: {
      hero: {
        title: "บทความและข่าวสาร",
        subtitle: "อัพเดตความรู้ด้านความงาม เทคนิคการดูแลผิว และข่าวสารจากคลินิก"
      },
      search: {
        placeholder: "ค้นหาบทความ...",
        button: "ค้นหา"
      },
      categories: {
        all: "ทั้งหมด",
        skincare: "สกินแคร์",
        treatments: "การรักษา",
        tips: "เคล็ดลับ",
        news: "ข่าวสาร"
      },
      posts: {
        skincare: {
          title: "5 วิธีดูแลผิวหน้าให้ขาวใสในฤดูหนาว",
          excerpt: "เคล็ดลับการดูแลผิวหน้าในฤดูหนาวให้ขาวใสและชุ่มชื้นตลอดเวลา",
          date: "15 ธ.ค. 2024"
        },
        botox: {
          title: "ทำความเข้าใจเรื่องโบท็อกซ์: ประโยชน์และสิ่งที่ควรรู้",
          excerpt: "คู่มือครอบคลุมเกี่ยวกับการรักษาด้วยโบท็อกซ์ ประโยชน์ ขั้นตอน และการดูแลหลังการรักษา",
          date: "12 ธ.ค. 2024"
        },
        laser: {
          title: "เลเซอร์ผิว: เทคโนโลยีใหม่สำหรับผิวใสไร้ตำหนิ",
          excerpt: "รู้จักกับเทคโนโลยีเลเซอร์ล่าสุดที่ช่วยให้ผิวกระจ่างใสและลดริ้วรอย",
          date: "10 ธ.ค. 2024"
        },
        wintercare: {
          title: "การดูแลผิวหน้าในฤดูหนาว",
          excerpt: "เทคนิคพิเศษในการดูแลผิวให้ชุ่มชื้นและป้องกันความแห้งกร้านในฤดูหนาว",
          date: "8 ธ.ค. 2024"
        },
        antiaging: {
          title: "10 นิสัยประจำวันเพื่อผิวอ่อนเยาว์",
          excerpt: "นิสัยง่ายๆ ที่สามารถทำได้ทุกวันเพื่อชะลอวัยและรักษาความอ่อนเยาว์",
          date: "5 ธ.ค. 2024"
        },
        consultation: {
          title: "การเตรียมตัวก่อนปรึกษาแพทย์เสริมความงาม",
          excerpt: "สิ่งที่ควรเตรียมและคำถามที่ควรถามก่อนเข้ารับการปรึกษาแพทย์เสริมความงาม",
          date: "3 ธ.ค. 2024"
        }
      },
      readMore: "อ่านต่อ",
      readTime: "เวลาอ่าน",
      author: "ผู้เขียน",
      tags: "แท็ก",
      pagination: {
        previous: "ก่อนหน้า",
        next: "ถัดไป"
      }
    },
    common: {
      next: "ถัดไป",
      previous: "ก่อนหน้า",
      and: "และ",
      loading: "กำลังโหลด...",
      error: "เกิดข้อผิดพลาด",
      retry: "ลองใหม่",
      bookNow: "จองเลย",
      learnMore: "เรียนรู้เพิ่มเติม",
      contactUs: "ติดต่อเรา",
      viewAll: "ดูทั้งหมด"
    }
  },
  en: {
    openHours: "Open daily 9:00 AM - 8:00 PM",
    nav: {
      home: "Home",
      services: "Services",
      about: "About",
      gallery: "Gallery",
      blog: "Blog",
      bookNow: "Book Now",
      login: "Login",
      signup: "Sign Up",
      dashboard: "Dashboard",
      logout: "Logout"
    },
    hero: {
      badge: 'Premium Beauty & Medical Aesthetics',
      title: 'Transform Your Beauty Journey',
      subtitle: 'Experience world-class treatments with our expert medical professionals. From skincare to advanced aesthetics, we help you achieve your beauty goals safely and effectively.',
      bookingTitle: 'Book Your Appointment',
      selectService: 'Select Service',
      selectDoctor: 'Select Doctor',
      selectBranch: 'Select Branch',
      bookButton: 'Book Appointment',
      happyClients: 'Happy Clients',
      yearsExperience: 'Years Experience',
      bookingModal: {
        title: 'Confirm Your Booking',
        description: 'Please review your appointment details before confirming.',
        summary: 'Appointment Summary',
        confirm: 'Confirm Booking'
      }
    },
    flashSales: {
      badge: 'Limited Time Offer',
      title: 'Flash Sale - Up to 50% Off',
      subtitle: 'Premium beauty treatments at unbeatable prices',
      cta: 'Claim Offer Now',
      timeLeft: 'Time Left'
    },
    services: {
      badge: 'Our Services',
      title: 'Premium Beauty Treatments',
      subtitle: 'Discover our comprehensive range of medical aesthetic treatments designed to enhance your natural beauty.',
      popular: 'Most Popular',
      starting: 'Starting from',
      learnMore: 'Learn More',
      viewAll: 'View All Services',
      facial: {
        title: 'Premium Facial Treatment',
        description: 'Rejuvenate your skin with our advanced facial treatments using medical-grade products.',
        price: '฿2,500'
      },
      botox: {
        title: 'Botox Treatment',
        description: 'Smooth away wrinkles and fine lines with precision Botox injections.',
        price: '฿8,000'
      },
      laser: {
        title: 'Laser Skin Rejuvenation',
        description: 'Advanced laser technology for skin resurfacing and anti-aging.',
        price: '฿12,000'
      }
    },
    beforeAfter: {
      badge: 'Real Results',
      title: 'See The Transformation',
      subtitle: 'Witness the remarkable results our clients have achieved with our premium treatments. Drag the slider to compare before and after photos.',
      viewGallery: 'View Full Gallery',
      comparison1: {
        title: 'Facial Rejuvenation',
        description: 'Advanced facial treatment with immediate visible results'
      },
      comparison2: {
        title: 'Skin Brightening',
        description: 'Professional skin brightening treatment for radiant complexion'
      },
      comparison3: {
        title: 'Anti-Aging Treatment',
        description: 'Comprehensive anti-aging solution for youthful appearance'
      }
    },
    testimonials: {
      badge: 'Client Reviews',
      title: 'What Our Clients Say',
      subtitle: 'Read genuine reviews from our satisfied clients who have experienced amazing transformations.',
      client1: {
        name: 'Sarah Johnson',
        location: 'Bangkok',
        rating: 5,
        review: 'The results exceeded my expectations! The staff is professional and the clinic is absolutely beautiful. I feel more confident than ever.',
        treatment: 'Facial Rejuvenation Package'
      },
      client2: {
        name: 'Michael Chen',
        location: 'Sukhumvit',
        rating: 5,
        review: 'Outstanding service and remarkable results. The doctors are highly skilled and made me feel comfortable throughout the entire process.',
        treatment: 'Botox Treatment'
      },
      client3: {
        name: 'Lisa Wong',
        location: 'Silom',
        rating: 5,
        review: 'Lullaby Clinic transformed my skin completely. The before and after difference is incredible. Highly recommend to anyone!',
        treatment: 'Laser Skin Treatment'
      }
    },
    gallery: {
      badge: 'Before & After',
      title: 'Amazing Transformations',
      subtitle: 'See the incredible results our clients have achieved with our expert treatments.',
      before: 'Before',
      after: 'After',
      viewMore: 'View More Results',
      item1: { treatment: 'Acne Scar Treatment', description: 'Laser treatment for smooth, clear skin' },
      item2: { treatment: 'Skin Brightening', description: 'Professional whitening for radiant complexion' },
      item3: { treatment: 'Anti-Aging Facial', description: 'Comprehensive facial rejuvenation treatment' },
      item4: { treatment: 'Botox Treatment', description: 'Wrinkle reduction for youthful appearance' },
      item5: { treatment: 'Pigmentation Removal', description: 'Advanced laser treatment for even skin tone' },
      item6: { treatment: 'Facial Contouring', description: 'Non-surgical face lifting and contouring' }
    },
    blog: {
      badge: 'Beauty Tips & News',
      title: 'Latest Articles',
      subtitle: 'Stay updated with the latest beauty trends, tips, and clinic news.',
      readMore: 'Read More',
      viewAll: 'View All Articles',
      searchPlaceholder: 'Search articles...',
      categories: {
        all: 'All',
        skincare: 'Skincare',
        treatments: 'Treatments',
        tips: 'Beauty Tips'
      },
      article1: {
        title: 'Winter Skincare: Essential Tips for Healthy Skin',
        excerpt: 'Learn how to protect and nourish your skin during the colder months with expert advice.',
        category: 'Skincare',
        readTime: '5 min read',
        date: 'Dec 15, 2024'
      },
      article2: {
        title: 'Understanding Botox: Benefits and What to Expect',
        excerpt: 'A comprehensive guide to Botox treatments, including benefits, process, and aftercare.',
        category: 'Treatments',
        readTime: '8 min read',
        date: 'Dec 12, 2024'
      },
      article3: {
        title: '10 Daily Habits for Radiant Skin',
        excerpt: 'Simple yet effective daily routines that can transform your skin health and appearance.',
        category: 'Beauty Tips',
        readTime: '6 min read',
        date: 'Dec 10, 2024'
      }
    },
    footer: {
      description: 'Transform your beauty journey with our world-class treatments and expert care.',
      quickLinks: 'Quick Links',
      services: 'Services',
      contact: 'Contact Info',
      followUs: 'Follow Us',
      newsletter: 'Newsletter',
      newsletterText: 'Subscribe for beauty tips and exclusive offers',
      emailPlaceholder: 'Your email',
      subscribe: 'Subscribe',
      copyright: '© 2024 Lullaby Clinic. All rights reserved.',
      privacy: 'Privacy Policy',
      terms: 'Terms of Service'
    },
    about: {
      hero: {
        title: "About Lullaby Clinic",
        subtitle: "Combining modern medical aesthetics with heartfelt care to help you feel confident in your own skin."
      },
      mission: {
        title: "Our Mission",
        description: "Provide safe, effective beauty treatments using the latest technology and evidence-based practices."
      },
      vision: {
        title: "Our Vision",
        description: "To be the most trusted clinic for holistic aesthetic care in Asia, setting new standards of excellence."
      },
      approach: {
        title: "Our Approach",
        description: "We believe in personalized treatment plans, continual innovation, and achieving natural-looking results that enhance your unique beauty. Every client receives comprehensive consultation and ongoing care from our expert team."
      },
      doctors: {
        title: "Meet Our Doctors",
        team: {
          dr_smith: {
            name: "Dr. Smith",
            role: "Founder & Medical Director"
          },
          dr_lee: {
            name: "Dr. Lee",
            role: "Dermatologist"
          },
          dr_chen: {
            name: "Dr. Chen",
            role: "Aesthetic Physician"
          }
        }
      },
      partners: {
        title: "Our Trusted Partners"
      },
      brand: {
        statement: "Transform your beauty journey with our world-class treatments and expert care."
      },
      quickLinks: {
        title: "Quick Links"
      },
      servicesSection: {
        title: "Services",
        learnMore: "Learn more"
      }
    },
    servicesPage: {
      hero: {
        title: "Our Services",
        subtitle: "Discover our comprehensive range of advanced beauty treatments with cutting-edge technology and expert medical professionals"
      },
      treatments: {
        title: "Our Treatments",
        facial: {
          title: "Premium Facial Treatment",
          description: "Advanced facial treatments using medical-grade products for radiant, youthful skin",
          duration: "60-90 minutes",
          price: "฿2,500-4,500",
          benefits: ["Reduce wrinkles and dark spots", "Increase hydration and elasticity", "Tighten pores", "Instant radiant skin"]
        },
        botox: {
          title: "Botox Treatment",
          description: "Reduce wrinkles and lift facial features with premium Botox from expert physicians",
          duration: "30-45 minutes",
          price: "฿8,000-15,000",
          benefits: ["Reduce forehead wrinkles", "Lift eyebrows and eyes", "Reduce crow's feet", "Natural-looking results"]
        },
        laser: {
          title: "Laser Skin Treatment",
          description: "Latest laser technology for skin resurfacing and anti-aging treatments",
          duration: "45-90 minutes",
          price: "฿5,000-12,000",
          benefits: ["Remove dark spots and melasma", "Reduce pore size", "Tighten skin", "Reduce acne scars"]
        },
        filler: {
          title: "Dermal Fillers",
          description: "Fill and contour facial features with premium fillers for natural-looking beauty",
          duration: "30-60 minutes",
          price: "฿12,000-25,000",
          benefits: ["Fill nasolabial folds", "Lift facial contours", "Nose reshaping", "Add volume to lips"]
        }
      },
      booking: {
        title: "Ready to Start Your Beauty Journey?",
        subtitle: "Book a free consultation with our expert physicians",
        button: "Book Appointment"
      }
    },
    doctorsPage: {
      hero: {
        title: "Our Expert Medical Team",
        subtitle: "Meet our professional medical team with extensive experience and expertise in aesthetic medicine"
      },
      team: {
        dr_smith: {
          name: "Dr. Smith Johnson",
          role: "Founder & Medical Director",
          specialties: ["Plastic Surgery", "Botox & Filler Injections", "Laser Treatments"],
          experience: "15+ years experience",
          education: ["MD from Chulalongkorn University", "Plastic Surgery Residency, Mahidol University", "Fellowship in South Korea"],
          description: "With over 15 years of experience, Dr. Smith is an internationally recognized expert in aesthetic medicine"
        },
        dr_lee: {
          name: "Dr. Lee Soo-yeon",
          role: "Dermatologist",
          specialties: ["Dermatology", "Laser Treatments", "Acne Treatment"],
          experience: "12+ years experience",
          education: ["MD from Chiang Mai University", "Dermatology Residency, Siriraj Hospital", "Fellowship in Japan"],
          description: "Expert in dermatology and laser treatments with extensive patient care experience"
        },
        dr_chen: {
          name: "Dr. Chen Wan-wan",
          role: "Aesthetic Physician",
          specialties: ["Dermal Fillers", "Botox", "Fat Surgery"],
          experience: "10+ years experience",
          education: ["MD from Thammasat University", "Plastic Surgery Residency, Ramathibodi Hospital", "Fellowship in Singapore"],
          description: "Specialist in filler and Botox injections with natural and safe results"
        }
      },
      appointment: {
        title: "Book Consultation with Our Doctors",
        button: "Book Appointment"
      }
    },
    contactPage: {
      hero: {
        title: "Contact Us",
        subtitle: "Ready to provide consultation and answer all your questions about beauty treatments"
      },
      info: {
        address: {
          title: "Address",
          value: "170 25 Moo 3, Samet Sub-district, Phra Ya Satchaa Road, Chonburi 20000"
        },
        phone: {
          title: "Phone",
          value: "************"
        },
        email: {
          title: "Email",
          value: "<EMAIL>"
        },
        hours: {
          title: "Opening Hours",
          value: "Monday-Saturday 9:00 AM - 6:00 PM"
        }
      },
      form: {
        title: "Send Us a Message",
        name: "Full Name",
        email: "Email",
        phone: "Phone Number",
        subject: "Subject",
        message: "Message",
        submit: "Send Message",
        success: "Message sent successfully. We will contact you within 24 hours",
        error: "An error occurred. Please try again"
      },
      map: {
        title: "Map"
      }
    },
    blogPage: {
      hero: {
        title: "Articles & News",
        subtitle: "Stay updated with beauty knowledge, skincare techniques, and clinic news"
      },
      search: {
        placeholder: "Search articles...",
        button: "Search"
      },
      categories: {
        all: "All",
        skincare: "Skincare",
        treatments: "Treatments",
        tips: "Tips",
        news: "News"
      },
      posts: {
        skincare: {
          title: "5 Ways to Keep Your Skin Bright and Clear in Winter",
          excerpt: "Tips for maintaining bright and moisturized skin throughout the winter season",
          date: "Dec 15, 2024"
        },
        botox: {
          title: "Understanding Botox: Benefits and What to Expect",
          excerpt: "Comprehensive guide about Botox treatments, benefits, procedures, and aftercare",
          date: "Dec 12, 2024"
        },
        laser: {
          title: "Laser Skin Treatment: New Technology for Flawless Skin",
          excerpt: "Learn about the latest laser technology that helps achieve radiant skin and reduce wrinkles",
          date: "Dec 10, 2024"
        },
        wintercare: {
          title: "Winter Facial Care",
          excerpt: "Special techniques for keeping skin moisturized and preventing dryness in winter",
          date: "Dec 8, 2024"
        },
        antiaging: {
          title: "10 Daily Habits for Youthful Skin",
          excerpt: "Simple daily habits that can slow aging and maintain youthful appearance",
          date: "Dec 5, 2024"
        },
        consultation: {
          title: "Preparing for Your Aesthetic Medicine Consultation",
          excerpt: "What to prepare and questions to ask before meeting with aesthetic medicine doctors",
          date: "Dec 3, 2024"
        }
      },
      readMore: "Read More",
      readTime: "Read Time",
      author: "Author",
      tags: "Tags",
      pagination: {
        previous: "Previous",
        next: "Next"
      }
    },
    common: {
      next: "Next",
      previous: "Previous",
      and: "and",
      loading: "Loading...",
      error: "An error occurred",
      retry: "Try Again",
      bookNow: "Book Now",
      learnMore: "Learn More",
      contactUs: "Contact Us",
      viewAll: "View All"
    }
  },
  zh: {
    openHours: "营业时间 周一至周六 9:00-18:00",
    nav: {
      home: "首页",
      services: "服务",
      about: "关于我们",
      gallery: "作品展示",
      blog: "博客",
      bookNow: "立即预约",
      login: '登录',
      signup: '注册',
      dashboard: '仪表板',
      logout: '退出',
    },
    hero: {
      badge: '高端美容与医疗美学',
      title: '让梦想成为现实',
      subtitle: '凭借尖端技术和专业医疗团队，我们准备为您提供最佳的美容护理。',
      bookingTitle: '预约服务',
      selectService: '选择服务',
      selectDoctor: '选择医生',
      selectBranch: '选择分店',
      bookButton: '预约挂号',
      happyClients: '满意客户',
      yearsExperience: '年经验',
      bookingModal: {
        title: '确认预约',
        description: '请查看您的预约详情',
        summary: '预约摘要',
        confirm: '确认预约'
      }
    },
    flashSales: {
      badge: '限时优惠',
      title: '闪购 - 高达50%折扣',
      subtitle: '无与伦比价格的高端美容治疗',
      cta: '立即领取优惠',
      timeLeft: '剩余时间'
    },
    services: {
      badge: '我们的服务',
      title: '高端美容治疗',
      subtitle: '发现我们全面的医疗美学治疗范围，旨在增强您的自然美。',
      popular: '热门',
      starting: '起价',
      learnMore: '了解更多',
      viewAll: '查看全部',
      facial: {
        title: '面部护理',
        description: '先进的面部护理，让肌肤焕发光彩',
        price: '฿2,500'
      },
      botox: {
        title: '肉毒素',
        description: '使用优质肉毒素减少皱纹，紧致面部',
        price: '฿8,000'
      },
      laser: {
        title: '激光治疗',
        description: '使用先进激光去除色斑和色素沉着',
        price: '฿12,000'
      }
    },
    beforeAfter: {
      badge: '真实效果',
      title: '见证蜕变',
      subtitle: '见证我们客户通过高端治疗取得的卓越成果。拖动滑块比较前后照片。',
      viewGallery: '查看完整画廊',
      comparison1: {
        title: '面部年轻化',
        description: '先进的面部治疗立即可见效果'
      },
      comparison2: {
        title: '肌肤美白',
        description: '专业肌肤美白治疗，打造光彩肌肤'
      },
      comparison3: {
        title: '抗衰老治疗',
        description: '全面抗衰老解决方案，打造年轻外观'
      }
    },
    testimonials: {
      badge: '客户评价',
      title: '客户怎么说',
      subtitle: '阅读满意客户的真实评价，他们体验了惊人的蜕变。',
      client1: {
        name: '张小姐',
        message: '效果太棒了！皮肤变亮了，斑点也淡化了。医生的咨询非常专业。',
        service: '面部护理'
      },
      client2: {
        name: '李女士',
        message: '这里的肉毒素效果很自然，面部平滑但不僵硬。很满意！',
        service: '肉毒素'
      },
      client3: {
        name: '王小姐',
        message: '激光治疗黄褐斑效果很好，治疗后皮肤亮了很多。',
        service: '激光治疗'
      }
    },
    gallery: {
      badge: '前后对比',
      title: '惊人蜕变',
      subtitle: '看看我们的客户通过专家治疗取得的令人难以置信的效果。',
      before: '之前',
      after: '之后',
      viewMore: '查看更多效果',
      item1: { treatment: '痤疮疤痕治疗', description: '激光治疗，肌肤光滑清洁' },
      item2: { treatment: '肌肤美白', description: '专业美白，肌肤光彩照人' },
      item3: { treatment: '抗衰老面部护理', description: '综合面部年轻化治疗' },
      item4: { treatment: '肉毒素治疗', description: '减少皱纹，外观年轻' },
      item5: { treatment: '色素沉着去除', description: '先进激光治疗，肌肤色调均匀' },
      item6: { treatment: '面部轮廓塑形', description: '非手术面部提升和轮廓塑形' }
    },
    blog: {
      badge: '美容贴士与新闻',
      title: '最新文章',
      subtitle: '了解最新美容趋势、贴士和诊所新闻。',
      readMore: '阅读更多',
      viewAll: '查看所有文章',
      searchPlaceholder: '搜索文章...',
      categories: {
        all: '全部',
        skincare: '护肤',
        treatments: '治疗',
        tips: '美容贴士'
      },
      article1: {
        title: '冬季护肤：健康肌肤的基本贴士',
        excerpt: '学习如何在寒冷的月份保护和滋养您的肌肤，专家建议。',
        category: '护肤',
        readTime: '5分钟阅读',
        date: '2024年12月15日'
      },
      article2: {
        title: '了解肉毒素：好处和预期效果',
        excerpt: '肉毒素治疗的综合指南，包括好处、过程和后期护理。',
        category: '治疗',
        readTime: '8分钟阅读',
        date: '2024年12月12日'
      },
      article3: {
        title: '光彩肌肤的10个日常习惯',
        excerpt: '简单而有效的日常护理，可以改变您的肌肤健康和外观。',
        category: '美容贴士',
        readTime: '6分钟阅读',
        date: '2024年12月10日'
      }
    },
    newsletter: {
      badge: '保持联系',
      title: '获取美容贴士和独家优惠',
      subtitle: '订阅我们的通讯，获取最新美容建议、治疗更新和特别促销。',
      emailPlaceholder: '输入您的邮箱地址',
      subscribe: '立即订阅',
      incentives: {
        title: '订阅者福利',
        item1: '20% off first treatment',
        item2: '月度美容贴士',
        item3: '独家促销',
        item4: '新服务抢先体验'
      },
      socialProof: '5,000+美容爱好者已订阅',
      successMessage: '感谢您的订阅！查看您的邮箱获取欢迎礼品。',
      errorMessage: '出了点问题。请重试。'
    },
    footer: {
      description: '通过我们的世界级治疗和专家护理，变革您的美丽之旅。',
      quickLinks: '快速链接',
      services: '服务',
      contact: '联系信息',
      followUs: '关注我们',
      newsletter: '新闻通讯',
      newsletterText: '订阅美容贴士和独家优惠',
      emailPlaceholder: '您的邮箱',
      subscribe: '订阅',
      copyright: '© 2024 摇篮诊所。保留所有权利。',
      privacy: '隐私政策',
      terms: '服务条款'
    },
    about: {
      hero: {
        title: "关于摇篮诊所",
        subtitle: "将现代医疗美学与贴心护理相结合，帮助您对自己的肌肤充满信心。"
      },
      mission: {
        title: "我们的使命",
        description: "使用最新技术和循证实践，提供安全有效的美容治疗。"
      },
      vision: {
        title: "我们的愿景",
        description: "成为亚洲最值得信赖的整体美学护理诊所，树立卓越新标准。"
      },
      approach: {
        title: "我们的方法",
        description: "我们相信个性化治疗方案、持续创新，并实现增强您独特美丽的自然效果。每位客户都会从我们的专家团队那里获得全面咨询和持续护理。"
      },
      doctors: {
        title: "认识我们的医生",
        team: {
          dr_smith: {
            name: "史密斯医生",
            role: "创始人兼医疗总监"
          },
          dr_lee: {
            name: "李医生",
            role: "皮肤科医生"
          },
          dr_chen: {
            name: "陈医生",
            role: "美容医师"
          }
        }
      },
      partners: {
        title: "我们值得信赖的合作伙伴"
      },
      brand: {
        statement: "通过我们的世界级治疗和专家护理，变革您的美丽之旅。"
      },
      quickLinks: {
        title: "快速链接"
      },
      servicesSection: {
        title: "服务",
        learnMore: "了解更多"
      }
    },
    servicesPage: {
      hero: {
        title: "我们的服务",
        subtitle: "探索我们全面的先进美容治疗项目，结合前沿技术和专业医疗团队"
      },
      treatments: {
        title: "我们的治疗项目",
        facial: {
          title: "高端面部护理",
          description: "使用医疗级产品的先进面部护理，打造光彩年轻肌肤",
          duration: "60-90分钟",
          price: "฿2,500-4,500",
          benefits: ["减少皱纹和色斑", "增加水分和弹性", "紧致毛孔", "即时光彩肌肤"]
        },
        botox: {
          title: "肉毒素治疗",
          description: "专业医师使用优质肉毒素减少皱纹和提升面部轮廓",
          duration: "30-45分钟",
          price: "฿8,000-15,000",
          benefits: ["减少额头皱纹", "提升眉眼轮廓", "减少鱼尾纹", "自然效果"]
        },
        laser: {
          title: "激光肌肤治疗",
          description: "最新激光技术用于肌肤重塑和抗衰老治疗",
          duration: "45-90分钟",
          price: "฿5,000-12,000",
          benefits: ["去除色斑和黄褐斑", "缩小毛孔", "紧致肌肤", "减少痘印"]
        },
        filler: {
          title: "玻尿酸填充",
          description: "使用优质填充剂塑造面部轮廓，呈现自然美感",
          duration: "30-60分钟",
          price: "฿12,000-25,000",
          benefits: ["填充法令纹", "提升面部轮廓", "鼻部塑形", "丰唇效果"]
        }
      },
      booking: {
        title: "准备开始您的美丽之旅了吗？",
        subtitle: "预约我们专业医师的免费咨询",
        button: "预约挂号"
      }
    },
    doctorsPage: {
      hero: {
        title: "我们的专业医疗团队",
        subtitle: "认识我们在美容医学领域拥有丰富经验和专业知识的专业医疗团队"
      },
      team: {
        dr_smith: {
          name: "史密斯·约翰逊医生",
          role: "创始人兼医疗总监",
          specialties: ["整形外科", "肉毒素和填充剂注射", "激光治疗"],
          experience: "15年以上经验",
          education: ["朱拉隆功大学医学博士", "玛希隆大学整形外科住院医师", "韩国进修学者"],
          description: "拥有超过15年的经验，史密斯医生是国际认可的美容医学专家"
        },
        dr_lee: {
          name: "李秀妍医生",
          role: "皮肤科医生",
          specialties: ["皮肤科", "激光治疗", "痤疮治疗"],
          experience: "12年以上经验",
          education: ["清迈大学医学博士", "诗里拉吉医院皮肤科住院医师", "日本进修学者"],
          description: "皮肤科和激光治疗专家，拥有丰富的患者护理经验"
        },
        dr_chen: {
          name: "陈婉婉医生",
          role: "美容医师",
          specialties: ["玻尿酸填充", "肉毒素", "脂肪手术"],
          experience: "10年以上经验",
          education: ["法政大学医学博士", "拉玛提博迪医院整形外科住院医师", "新加坡进修学者"],
          description: "专精于填充剂和肉毒素注射，效果自然安全"
        }
      },
      appointment: {
        title: "预约医生咨询",
        button: "预约挂号"
      }
    },
    contactPage: {
      hero: {
        title: "联系我们",
        subtitle: "准备为您提供咨询并回答有关美容治疗的所有问题"
      },
      info: {
        address: {
          title: "地址",
          value: "泰国春武里府萨迈县帕亚萨查路170/25号村3号 20000"
        },
        phone: {
          title: "电话",
          value: "************"
        },
        email: {
          title: "邮箱",
          value: "<EMAIL>"
        },
        hours: {
          title: "营业时间",
          value: "周一至周六 上午9:00 - 下午6:00"
        }
      },
      form: {
        title: "发送消息给我们",
        name: "姓名",
        email: "邮箱",
        phone: "电话号码",
        subject: "主题",
        message: "消息",
        submit: "发送消息",
        success: "消息发送成功。我们将在24小时内联系您",
        error: "发生错误，请重试"
      },
      map: {
        title: "地图"
      }
    },
    blogPage: {
      hero: {
        title: "文章与新闻",
        subtitle: "了解最新美容知识、护肤技巧和诊所新闻"
      },
      search: {
        placeholder: "搜索文章...",
        button: "搜索"
      },
      categories: {
        all: "全部",
        skincare: "护肤",
        treatments: "治疗",
        tips: "技巧",
        news: "新闻"
      },
      posts: {
        skincare: {
          title: "冬季保持肌肤亮白清洁的5种方法",
          excerpt: "在整个冬季保持肌肤亮白和滋润的技巧",
          date: "2024年12月15日"
        },
        botox: {
          title: "了解肉毒素：好处和期待效果",
          excerpt: "关于肉毒素治疗的全面指南，包括好处、程序和后续护理",
          date: "2024年12月12日"
        },
        laser: {
          title: "激光肌肤治疗：完美肌肤的新技术",
          excerpt: "了解最新的激光技术，帮助实现光彩肌肤并减少皱纹",
          date: "2024年12月10日"
        },
        wintercare: {
          title: "冬季面部护理",
          excerpt: "保持肌肤滋润和防止冬季干燥的特殊技巧",
          date: "2024年12月8日"
        },
        antiaging: {
          title: "年轻肌肤的10个日常习惯",
          excerpt: "可以延缓衰老并保持年轻外观的简单日常习惯",
          date: "2024年12月5日"
        },
        consultation: {
          title: "准备美容医学咨询",
          excerpt: "在会见美容医学医生之前需要准备什么和要问的问题",
          date: "2024年12月3日"
        }
      },
      readMore: "阅读更多",
      readTime: "阅读时间",
      author: "作者",
      tags: "标签",
      pagination: {
        previous: "上一页",
        next: "下一页"
      }
    },
    common: {
      next: "下一个",
      previous: "上一个",
      and: "和",
      loading: "加载中...",
      error: "发生错误",
      retry: "重试",
      bookNow: "立即预约",
      learnMore: "了解更多",
      contactUs: "联系我们",
      viewAll: "查看全部"
    }
  }
};

export const getTranslation = (language: string): Translation => {
  return translations[language] || translations.en;
};

export default translations;
