/**
 * Lullaby Clinic - Hybrid Services
 * Service layer that combines Supabase and Payload CMS data
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { payloadContentService, type PayloadDoctor, type PayloadService, type PayloadBlogPost } from './payload-services';
import { doctorService, serviceService, contentService } from './supabase-services';
import type { Doctor, Service, BlogPost } from '@/types/database';
import type { ApiResponse } from '@/types/api';

// Configuration for data source priority
const DATA_SOURCE_CONFIG = {
  // Primary source for each data type
  doctors: 'payload', // Use Payload CMS for doctor management
  services: 'payload', // Use Payload CMS for service management
  blogPosts: 'payload', // Use Payload CMS for content management
  appointments: 'supabase', // Use Supabase for appointment booking
  users: 'supabase', // Use Supabase for user authentication
  payments: 'supabase', // Use Supabase for payment processing
} as const;

// Data transformation utilities
const transformPayloadDoctor = (payloadDoctor: PayloadDoctor): Doctor => ({
  id: payloadDoctor.id,
  user_id: payloadDoctor.user.id,
  first_name: payloadDoctor.user.firstName,
  last_name: payloadDoctor.user.lastName,
  email: payloadDoctor.user.email,
  phone: payloadDoctor.user.phone || '',
  license_number: payloadDoctor.licenseNumber,
  specializations: payloadDoctor.specializations.map(s => s.specialty),
  qualifications: payloadDoctor.qualifications || '',
  experience_years: payloadDoctor.experience || 0,
  consultation_fee: payloadDoctor.consultationFee || 0,
  working_hours: payloadDoctor.workingHours || {},
  profile_image_url: payloadDoctor.profileImage?.url || '',
  is_active: payloadDoctor.isActive,
  created_at: payloadDoctor.createdAt,
  updated_at: payloadDoctor.updatedAt,
});

const transformPayloadService = (payloadService: PayloadService): Service => ({
  id: payloadService.id,
  name: payloadService.name,
  description: payloadService.description || '',
  category: payloadService.category,
  duration_minutes: payloadService.duration,
  base_price: payloadService.price,
  is_active: payloadService.isActive,
  requires_preparation: payloadService.requiresPreparation || false,
  preparation_instructions: payloadService.preparationInstructions || '',
  created_at: payloadService.createdAt,
  updated_at: payloadService.updatedAt,
});

const transformPayloadBlogPost = (payloadPost: PayloadBlogPost): BlogPost => ({
  id: payloadPost.id,
  title: payloadPost.title,
  content: payloadPost.content,
  excerpt: payloadPost.excerpt || '',
  slug: payloadPost.slug,
  category: payloadPost.category,
  difficulty: payloadPost.difficulty || 'beginner',
  language: payloadPost.language,
  is_published: payloadPost.isPublished,
  is_featured: payloadPost.isFeatured || false,
  published_at: payloadPost.publishedAt || payloadPost.createdAt,
  author_name: `${payloadPost.author.firstName} ${payloadPost.author.lastName}`,
  featured_image_url: payloadPost.featuredImage?.url || '',
  tags: payloadPost.tags || [],
  created_at: payloadPost.createdAt,
  updated_at: payloadPost.updatedAt,
});

// Hybrid service implementations
export const hybridDoctorService = {
  async getDoctors(filters?: { specialty?: string; isActive?: boolean }) {
    try {
      // Try Payload CMS first
      const payloadResponse = await payloadContentService.getDoctors({
        specialty: filters?.specialty,
        isActive: filters?.isActive,
        limit: 100,
      });

      if (payloadResponse.success && payloadResponse.data) {
        const transformedDoctors = payloadResponse.data.docs.map(transformPayloadDoctor);
        return {
          success: true,
          data: transformedDoctors,
          source: 'payload',
          error: null,
        };
      }

      // Fallback to Supabase
      console.warn('Payload CMS unavailable, falling back to Supabase for doctors');
      const supabaseResponse = await doctorService.getDoctors();
      return {
        ...supabaseResponse,
        source: 'supabase',
      };
    } catch (error) {
      console.error('Error fetching doctors:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'error',
      };
    }
  },

  async getDoctor(id: string) {
    try {
      // Try Payload CMS first
      const payloadResponse = await payloadContentService.getDoctor(id);

      if (payloadResponse.success && payloadResponse.data) {
        return {
          success: true,
          data: transformPayloadDoctor(payloadResponse.data),
          source: 'payload',
          error: null,
        };
      }

      // Fallback to Supabase
      console.warn('Payload CMS unavailable, falling back to Supabase for doctor');
      const supabaseResponse = await doctorService.getDoctor(id);
      return {
        ...supabaseResponse,
        source: 'supabase',
      };
    } catch (error) {
      console.error('Error fetching doctor:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'error',
      };
    }
  },
};

export const hybridServiceService = {
  async getServices(filters?: { category?: string; isActive?: boolean }) {
    try {
      // Try Payload CMS first
      const payloadResponse = await payloadContentService.getServices({
        category: filters?.category,
        isActive: filters?.isActive,
        limit: 100,
      });

      if (payloadResponse.success && payloadResponse.data) {
        const transformedServices = payloadResponse.data.docs.map(transformPayloadService);
        return {
          success: true,
          data: transformedServices,
          source: 'payload',
          error: null,
        };
      }

      // Fallback to Supabase
      console.warn('Payload CMS unavailable, falling back to Supabase for services');
      const supabaseResponse = await serviceService.getServices();
      return {
        ...supabaseResponse,
        source: 'supabase',
      };
    } catch (error) {
      console.error('Error fetching services:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'error',
      };
    }
  },

  async getService(id: string) {
    try {
      // Try Payload CMS first
      const payloadResponse = await payloadContentService.getService(id);

      if (payloadResponse.success && payloadResponse.data) {
        return {
          success: true,
          data: transformPayloadService(payloadResponse.data),
          source: 'payload',
          error: null,
        };
      }

      // Fallback to Supabase
      console.warn('Payload CMS unavailable, falling back to Supabase for service');
      const supabaseResponse = await serviceService.getService(id);
      return {
        ...supabaseResponse,
        source: 'supabase',
      };
    } catch (error) {
      console.error('Error fetching service:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'error',
      };
    }
  },
};

export const hybridContentService = {
  async getBlogPosts(filters?: {
    category?: string;
    language?: string;
    featured?: boolean;
    limit?: number;
  }) {
    try {
      // Try Payload CMS first
      const payloadResponse = await payloadContentService.getBlogPosts({
        category: filters?.category,
        language: filters?.language,
        isFeatured: filters?.featured,
        isPublished: true,
        limit: filters?.limit || 20,
      });

      if (payloadResponse.success && payloadResponse.data) {
        const transformedPosts = payloadResponse.data.docs.map(transformPayloadBlogPost);
        return {
          success: true,
          data: transformedPosts,
          source: 'payload',
          error: null,
        };
      }

      // Fallback to Supabase
      console.warn('Payload CMS unavailable, falling back to Supabase for blog posts');
      const supabaseResponse = await contentService.getBlogPosts(filters);
      return {
        ...supabaseResponse,
        source: 'supabase',
      };
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'error',
      };
    }
  },

  async getBlogPost(id: string) {
    try {
      // Try Payload CMS first
      const payloadResponse = await payloadContentService.getBlogPost(id);

      if (payloadResponse.success && payloadResponse.data) {
        return {
          success: true,
          data: transformPayloadBlogPost(payloadResponse.data),
          source: 'payload',
          error: null,
        };
      }

      // Fallback to Supabase
      console.warn('Payload CMS unavailable, falling back to Supabase for blog post');
      const supabaseResponse = await contentService.getBlogPost(id);
      return {
        ...supabaseResponse,
        source: 'supabase',
      };
    } catch (error) {
      console.error('Error fetching blog post:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'error',
      };
    }
  },
};

// Health check for both systems
export const hybridHealthService = {
  async checkSystemHealth() {
    const [payloadHealth, supabaseHealth] = await Promise.allSettled([
      payloadContentService.healthCheck(),
      // Add Supabase health check if available
      Promise.resolve(true), // Placeholder for Supabase health check
    ]);

    return {
      payload: {
        status: payloadHealth.status === 'fulfilled' && payloadHealth.value ? 'healthy' : 'unhealthy',
        available: payloadHealth.status === 'fulfilled' && payloadHealth.value,
      },
      supabase: {
        status: supabaseHealth.status === 'fulfilled' && supabaseHealth.value ? 'healthy' : 'unhealthy',
        available: supabaseHealth.status === 'fulfilled' && supabaseHealth.value,
      },
      overall: {
        status: (payloadHealth.status === 'fulfilled' && payloadHealth.value) || 
                (supabaseHealth.status === 'fulfilled' && supabaseHealth.value) ? 'healthy' : 'unhealthy',
      },
    };
  },
};

// Export the hybrid services as the default
export default {
  doctors: hybridDoctorService,
  services: hybridServiceService,
  content: hybridContentService,
  health: hybridHealthService,
};
