
/**
 * Lullaby Clinic - Email Notification Service
 * Comprehensive email system for appointment notifications
 * 
 * @version 2.0.0
 * @updated 2024-12-19
 */

import { supabase, db } from './supabase';
import type { 
  Appointment, 
  UserProfile, 
  Doctor, 
  Service, 
  NotificationType,
  NotificationChannel 
} from '@/types/database';
import type { <PERSON><PERSON> } from '@/types/database';

// Email request interface for Edge Function
interface EmailRequest {
  to: string;
  type: 'confirmation' | 'reminder' | 'cancellation' | 'rescheduled';
  appointmentData: {
    id: string;
    patientName: string;
    doctorName: string;
    serviceName: string;
    appointmentDate: string;
    appointmentTime: string;
    totalAmount: number;
    notes?: string;
  };
  language?: string;
}

// Email service response from Edge Function
interface EmailResponse {
  success: boolean;
  messageId?: string;
  subject?: string;
  error?: string;
}

// Email service configuration
const EMAIL_CONFIG = {
  FROM_EMAIL: '<EMAIL>',
  FROM_NAME: 'Lullaby Clinic',
  REPLY_TO: '<EMAIL>',
  CLINIC_PHONE: '************',
  CLINIC_ADDRESS: '170 25 Moo 3, Samet Sub-district, Phaya Satcha Rd, Chon Buri 20000',
  WEBSITE_URL: 'https://lullabyclinic.com'
};

// Email service class
export class EmailService {
  private async logNotification(
    recipientId: string,
    type: NotificationType,
    channel: NotificationChannel,
    subject: string,
    content: string,
    appointmentId?: string,
    metadata?: Record<string, unknown>
  ) {
    try {
      await db.notifications().insert({
        recipient_id: recipientId,
        type,
        channel,
        subject,
        content,
        appointment_id: appointmentId,
        sent_at: new Date().toISOString(),
        delivery_status: 'sent',
        metadata: metadata as Json
      });
    } catch (error) {
      console.error('Failed to log notification:', error);
    }
  }

  private formatDateTime(dateString: string, language: 'th' | 'en') {
    const date = new Date(dateString);
    const dateOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    };
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    };

    const locale = language === 'th' ? 'th-TH' : 'en-US';
    
    return {
      date: date.toLocaleDateString(locale, dateOptions),
      time: date.toLocaleTimeString(locale, timeOptions)
    };
  }

  // Send appointment confirmation email
  async sendAppointmentConfirmation(
    appointment: Appointment & {
      patient?: UserProfile;
      doctor?: Doctor & { user_profile?: UserProfile };
      service?: Service;
    },
    language: 'th' | 'en' = 'th'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!appointment.patient?.email) {
        throw new Error('Patient email not found');
      }

      const { date, time } = this.formatDateTime(appointment.appointment_date, language);
      
      const appointmentData = {
        id: appointment.id,
        patientName: `${appointment.patient.first_name} ${appointment.patient.last_name}`,
        doctorName: `${appointment.doctor?.user_profile?.first_name} ${appointment.doctor?.user_profile?.last_name}`,
        serviceName: appointment.service?.name || 'Unknown Service',
        appointmentDate: date,
        appointmentTime: time,
        totalAmount: appointment.total_amount || 0,
        notes: appointment.patient_notes || ''
      };

      const emailRequest: EmailRequest = {
        to: appointment.patient.email,
        type: 'confirmation',
        appointmentData,
        language
      };

      const { data, error } = await supabase.functions.invoke('send-appointment-email', {
        body: emailRequest
      });

      if (error) {
        console.error('Edge Function error:', error);
        return { 
          success: false, 
          error: `Edge Function error: ${error.message}` 
        };
      }

      if (!data?.success) {
        console.error('Email sending failed:', data?.error);
        return { 
          success: false, 
          error: data?.error || 'Unknown email sending error' 
        };
      }

      // Log the notification
      await this.logNotification(
        appointment.patient_id,
        'appointment_confirmation',
        'email',
        data.subject || 'Appointment Confirmation',
        `Appointment confirmation sent for ${appointmentData.appointmentDate} at ${appointmentData.appointmentTime}`,
        appointment.id,
        { messageId: data.messageId, language }
      );

      return { success: true };
    } catch (error) {
      console.error('Failed to send appointment confirmation:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Send appointment reminder email
  async sendAppointmentReminder(
    appointment: Appointment & {
      patient?: UserProfile;
      doctor?: Doctor & { user_profile?: UserProfile };
      service?: Service;
    },
    language: 'th' | 'en' = 'th'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!appointment.patient?.email) {
        throw new Error('Patient email not found');
      }

      const { date, time } = this.formatDateTime(appointment.appointment_date, language);
      
      const appointmentData = {
        id: appointment.id,
        patientName: `${appointment.patient.first_name} ${appointment.patient.last_name}`,
        doctorName: `${appointment.doctor?.user_profile?.first_name} ${appointment.doctor?.user_profile?.last_name}`,
        serviceName: appointment.service?.name || 'Unknown Service',
        appointmentDate: date,
        appointmentTime: time,
        totalAmount: appointment.total_amount || 0,
        notes: appointment.patient_notes || ''
      };

      const emailRequest: EmailRequest = {
        to: appointment.patient.email,
        type: 'reminder',
        appointmentData,
        language
      };

      const { data, error } = await supabase.functions.invoke('send-appointment-email', {
        body: emailRequest
      });

      if (error) {
        console.error('Edge Function error:', error);
        return { 
          success: false, 
          error: `Edge Function error: ${error.message}` 
        };
      }

      if (!data?.success) {
        console.error('Email sending failed:', data?.error);
        return { 
          success: false, 
          error: data?.error || 'Unknown email sending error' 
        };
      }

      // Log the notification
      await this.logNotification(
        appointment.patient_id,
        'appointment_reminder',
        'email',
        data.subject || 'Appointment Reminder',
        `Appointment reminder sent for ${appointmentData.appointmentDate} at ${appointmentData.appointmentTime}`,
        appointment.id,
        { messageId: data.messageId, language }
      );

      // Update appointment to mark reminder as sent
      await db.appointments()
        .update({ reminder_sent_at: new Date().toISOString() })
        .eq('id', appointment.id);

      return { success: true };
    } catch (error) {
      console.error('Failed to send appointment reminder:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Send appointment cancellation email
  async sendAppointmentCancellation(
    appointment: Appointment & {
      patient?: UserProfile;
      doctor?: Doctor & { user_profile?: UserProfile };
      service?: Service;
    },
    language: 'th' | 'en' = 'th'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!appointment.patient?.email) {
        throw new Error('Patient email not found');
      }

      const { date, time } = this.formatDateTime(appointment.appointment_date, language);
      
      const appointmentData = {
        id: appointment.id,
        patientName: `${appointment.patient.first_name} ${appointment.patient.last_name}`,
        doctorName: `${appointment.doctor?.user_profile?.first_name} ${appointment.doctor?.user_profile?.last_name}`,
        serviceName: appointment.service?.name || 'Unknown Service',
        appointmentDate: date,
        appointmentTime: time,
        totalAmount: appointment.total_amount || 0,
        notes: appointment.patient_notes || ''
      };

      const emailRequest: EmailRequest = {
        to: appointment.patient.email,
        type: 'cancellation',
        appointmentData,
        language
      };

      const { data, error } = await supabase.functions.invoke('send-appointment-email', {
        body: emailRequest
      });

      if (error) {
        console.error('Edge Function error:', error);
        return { 
          success: false, 
          error: `Edge Function error: ${error.message}` 
        };
      }

      if (!data?.success) {
        console.error('Email sending failed:', data?.error);
        return { 
          success: false, 
          error: data?.error || 'Unknown email sending error' 
        };
      }

      // Log the notification
      await this.logNotification(
        appointment.patient_id,
        'appointment_confirmation', // Using confirmation type for cancellation
        'email',
        data.subject || 'Appointment Cancelled',
        `Appointment cancellation sent for ${appointmentData.appointmentDate} at ${appointmentData.appointmentTime}`,
        appointment.id,
        { messageId: data.messageId, language, cancelled: true }
      );

      return { success: true };
    } catch (error) {
      console.error('Failed to send appointment cancellation:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Send batch appointment reminders (for scheduled job)
  async sendBatchAppointmentReminders(): Promise<{ sent: number; failed: number }> {
    try {
      // Get appointments for tomorrow that haven't had reminders sent
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowStart = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate());
      const tomorrowEnd = new Date(tomorrowStart);
      tomorrowEnd.setDate(tomorrowEnd.getDate() + 1);

      const { data: appointments } = await db.appointments()
        .select(`
          *,
          patient:user_profiles(*),
          doctor:doctors(
            *,
            user_profile:user_profiles(*)
          ),
          service:services(*)
        `)
        .gte('appointment_date', tomorrowStart.toISOString())
        .lt('appointment_date', tomorrowEnd.toISOString())
        .in('status', ['scheduled', 'confirmed'])
        .is('reminder_sent_at', null);

      let sent = 0;
      let failed = 0;

      for (const appointment of appointments || []) {
        try {
          const language = appointment.patient?.preferred_language === 'en' ? 'en' : 'th';
          const result = await this.sendAppointmentReminder(appointment, language);
          
          if (result.success) {
            sent++;
          } else {
            failed++;
          }
        } catch (error) {
          console.error(`Failed to send reminder for appointment ${appointment.id}:`, error);
          failed++;
        }
      }

      return { sent, failed };
    } catch (error) {
      console.error('Failed to send batch appointment reminders:', error);
      return { sent: 0, failed: 0 };
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();
