/**
 * Lullaby Clinic - Authentication Integration
 * Coordinates between Supabase auth and Payload CMS auth
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { supabase } from './supabase/client';
import { payloadApi } from './payload-api';
import type { User, Session } from '@supabase/supabase-js';
import type { UserProfile } from '@/types/database';

// Auth integration configuration
export const AUTH_INTEGRATION_CONFIG = {
  // Whether to sync users between systems
  SYNC_USERS: true,
  // Whether to create Payload users automatically
  AUTO_CREATE_PAYLOAD_USERS: true,
  // Default role for new Payload users
  DEFAULT_PAYLOAD_ROLE: 'patient',
  // Whether to sync profile updates
  SYNC_PROFILE_UPDATES: true,
} as const;

// User synchronization interface
interface UserSyncData {
  supabaseUser: User;
  supabaseProfile?: UserProfile;
  payloadUser?: any;
  syncStatus: 'synced' | 'pending' | 'failed' | 'not_required';
}

// Authentication integration service
export class AuthIntegrationService {
  /**
   * Sync Supabase user to Payload CMS
   */
  async syncUserToPayload(user: User, profile?: UserProfile): Promise<UserSyncData> {
    if (!AUTH_INTEGRATION_CONFIG.SYNC_USERS) {
      return {
        supabaseUser: user,
        supabaseProfile: profile,
        syncStatus: 'not_required',
      };
    }

    try {
      // Check if user already exists in Payload
      const existingUserResponse = await payloadApi.get(`/users`, {
        where: {
          email: { equals: user.email },
        },
      });

      let payloadUser;

      if (existingUserResponse.success && existingUserResponse.data?.docs?.length > 0) {
        // User exists, update if needed
        payloadUser = existingUserResponse.data.docs[0];
        
        if (AUTH_INTEGRATION_CONFIG.SYNC_PROFILE_UPDATES && profile) {
          const updateData = {
            firstName: profile.first_name,
            lastName: profile.last_name,
            phone: profile.phone,
          };

          const updateResponse = await payloadApi.patch(`/users/${payloadUser.id}`, updateData);
          if (updateResponse.success) {
            payloadUser = updateResponse.data;
          }
        }
      } else if (AUTH_INTEGRATION_CONFIG.AUTO_CREATE_PAYLOAD_USERS) {
        // Create new user in Payload
        const createData = {
          email: user.email,
          role: AUTH_INTEGRATION_CONFIG.DEFAULT_PAYLOAD_ROLE,
          firstName: profile?.first_name || user.user_metadata?.first_name || '',
          lastName: profile?.last_name || user.user_metadata?.last_name || '',
          phone: profile?.phone || user.user_metadata?.phone || '',
          isActive: true,
          // Don't set password - this user will only authenticate via Supabase
        };

        const createResponse = await payloadApi.post('/users', createData);
        if (createResponse.success) {
          payloadUser = createResponse.data;
        }
      }

      return {
        supabaseUser: user,
        supabaseProfile: profile,
        payloadUser,
        syncStatus: payloadUser ? 'synced' : 'failed',
      };
    } catch (error) {
      console.error('Failed to sync user to Payload:', error);
      return {
        supabaseUser: user,
        supabaseProfile: profile,
        syncStatus: 'failed',
      };
    }
  }

  /**
   * Get user permissions from Payload CMS
   */
  async getUserPermissions(email: string): Promise<{
    role?: string;
    permissions?: string[];
    isActive?: boolean;
  }> {
    try {
      const response = await payloadApi.get('/users', {
        where: {
          email: { equals: email },
        },
        select: {
          role: true,
          isActive: true,
        },
      });

      if (response.success && response.data?.docs?.length > 0) {
        const user = response.data.docs[0];
        return {
          role: user.role,
          isActive: user.isActive,
          permissions: this.getRolePermissions(user.role),
        };
      }

      return {};
    } catch (error) {
      console.error('Failed to get user permissions:', error);
      return {};
    }
  }

  /**
   * Get permissions based on role
   */
  private getRolePermissions(role: string): string[] {
    const rolePermissions: Record<string, string[]> = {
      admin: [
        'manage_users',
        'manage_doctors',
        'manage_services',
        'manage_appointments',
        'manage_content',
        'view_analytics',
        'manage_settings',
      ],
      doctor: [
        'view_appointments',
        'manage_own_appointments',
        'view_patients',
        'manage_medical_records',
        'view_own_schedule',
      ],
      receptionist: [
        'view_appointments',
        'manage_appointments',
        'view_patients',
        'manage_patient_info',
        'view_doctors',
        'view_services',
      ],
      patient: [
        'view_own_appointments',
        'book_appointments',
        'view_own_medical_records',
        'update_own_profile',
      ],
    };

    return rolePermissions[role] || [];
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(email: string, permission: string): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(email);
    return userPermissions.permissions?.includes(permission) || false;
  }

  /**
   * Get enhanced user session with Payload data
   */
  async getEnhancedSession(): Promise<{
    session: Session | null;
    user: User | null;
    profile: UserProfile | null;
    payloadUser: any;
    permissions: string[];
    role: string | null;
  }> {
    try {
      // Get Supabase session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        return {
          session: null,
          user: null,
          profile: null,
          payloadUser: null,
          permissions: [],
          role: null,
        };
      }

      // Get Supabase profile
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', session.user.id)
        .single();

      // Get Payload user data and permissions
      const payloadPermissions = await this.getUserPermissions(session.user.email!);

      // Sync user if needed
      const syncResult = await this.syncUserToPayload(session.user, profile);

      return {
        session,
        user: session.user,
        profile,
        payloadUser: syncResult.payloadUser,
        permissions: payloadPermissions.permissions || [],
        role: payloadPermissions.role || null,
      };
    } catch (error) {
      console.error('Failed to get enhanced session:', error);
      return {
        session: null,
        user: null,
        profile: null,
        payloadUser: null,
        permissions: [],
        role: null,
      };
    }
  }

  /**
   * Handle user sign out from both systems
   */
  async signOut(): Promise<void> {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();
      
      // Note: Payload CMS doesn't need explicit sign out since we're not storing sessions there
      // The user authentication is handled entirely by Supabase
    } catch (error) {
      console.error('Failed to sign out:', error);
      throw error;
    }
  }

  /**
   * Handle authentication state changes
   */
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      // Sync user on sign in
      if (event === 'SIGNED_IN' && session?.user) {
        try {
          await this.syncUserToPayload(session.user);
        } catch (error) {
          console.warn('Failed to sync user on sign in:', error);
        }
      }

      callback(event, session);
    });
  }
}

// Export singleton instance
export const authIntegration = new AuthIntegrationService();

// Export utility functions
export const syncUserToPayload = (user: User, profile?: UserProfile) => 
  authIntegration.syncUserToPayload(user, profile);

export const getUserPermissions = (email: string) => 
  authIntegration.getUserPermissions(email);

export const hasPermission = (email: string, permission: string) => 
  authIntegration.hasPermission(email, permission);

export const getEnhancedSession = () => 
  authIntegration.getEnhancedSession();

export default authIntegration;
