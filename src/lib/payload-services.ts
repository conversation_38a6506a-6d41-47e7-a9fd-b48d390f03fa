/**
 * Lullaby Clinic - Payload CMS Services
 * Service functions for content management operations
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { payloadApi } from './payload-api';
import type { ApiResponse, PaginatedResponse } from '@/types/api';

// Payload CMS Collection Types
export interface PayloadDoctor {
  id: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
  };
  licenseNumber: string;
  specializations: Array<{ specialty: string }>;
  qualifications?: string;
  experience?: number;
  consultationFee?: number;
  workingHours?: {
    monday?: { start: string; end: string; isWorking: boolean };
    tuesday?: { start: string; end: string; isWorking: boolean };
    wednesday?: { start: string; end: string; isWorking: boolean };
    thursday?: { start: string; end: string; isWorking: boolean };
    friday?: { start: string; end: string; isWorking: boolean };
    saturday?: { start: string; end: string; isWorking: boolean };
    sunday?: { start: string; end: string; isWorking: boolean };
  };
  profileImage?: PayloadMedia;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PayloadService {
  id: string;
  name: string;
  description?: string;
  category: string;
  duration: number;
  price: number;
  isActive: boolean;
  requiresPreparation?: boolean;
  preparationInstructions?: string;
  availableDoctors?: PayloadDoctor[];
  createdAt: string;
  updatedAt: string;
}

export interface PayloadAppointment {
  id: string;
  patient: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
      email: string;
      phone?: string;
    };
  };
  doctor: PayloadDoctor;
  service: PayloadService;
  appointmentDate: string;
  appointmentTime: string;
  duration: number;
  status: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
  symptoms?: string;
  diagnosis?: string;
  prescription?: string;
  followUpRequired?: boolean;
  followUpDate?: string;
  totalAmount?: number;
  paymentStatus: 'pending' | 'paid' | 'partial' | 'refunded';
  createdAt: string;
  updatedAt: string;
}

export interface PayloadBlogPost {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  category: string;
  difficulty?: string;
  language: string;
  isPublished: boolean;
  isFeatured?: boolean;
  publishedAt?: string;
  author: {
    firstName: string;
    lastName: string;
  };
  featuredImage?: PayloadMedia;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface PayloadMedia {
  id: string;
  filename: string;
  mimeType: string;
  filesize: number;
  width?: number;
  height?: number;
  alt?: string;
  url: string;
  createdAt: string;
  updatedAt: string;
}

export interface PayloadUser {
  id: string;
  email: string;
  role: 'admin' | 'doctor' | 'receptionist' | 'patient';
  firstName: string;
  lastName: string;
  phone?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Service Functions
export const payloadContentService = {
  // Doctor Services
  async getDoctors(params?: { 
    limit?: number; 
    page?: number; 
    specialty?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<PaginatedResponse<PayloadDoctor>>> {
    const queryParams = {
      limit: params?.limit || 50,
      page: params?.page || 1,
      ...(params?.specialty && { 'specializations.specialty': params.specialty }),
      ...(params?.isActive !== undefined && { isActive: params.isActive }),
    };

    return payloadApi.get<PaginatedResponse<PayloadDoctor>>('/doctors', queryParams);
  },

  async getDoctor(id: string): Promise<ApiResponse<PayloadDoctor>> {
    return payloadApi.get<PayloadDoctor>(`/doctors/${id}`);
  },

  // Service Services
  async getServices(params?: {
    limit?: number;
    page?: number;
    category?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<PaginatedResponse<PayloadService>>> {
    const queryParams = {
      limit: params?.limit || 50,
      page: params?.page || 1,
      ...(params?.category && { category: params.category }),
      ...(params?.isActive !== undefined && { isActive: params.isActive }),
    };

    return payloadApi.get<PaginatedResponse<PayloadService>>('/services', queryParams);
  },

  async getService(id: string): Promise<ApiResponse<PayloadService>> {
    return payloadApi.get<PayloadService>(`/services/${id}`);
  },

  // Appointment Services
  async getAppointments(params?: {
    limit?: number;
    page?: number;
    doctorId?: string;
    patientId?: string;
    status?: string;
    date?: string;
  }): Promise<ApiResponse<PaginatedResponse<PayloadAppointment>>> {
    const queryParams = {
      limit: params?.limit || 50,
      page: params?.page || 1,
      ...(params?.doctorId && { doctor: params.doctorId }),
      ...(params?.patientId && { patient: params.patientId }),
      ...(params?.status && { status: params.status }),
      ...(params?.date && { appointmentDate: params.date }),
    };

    return payloadApi.get<PaginatedResponse<PayloadAppointment>>('/appointments', queryParams);
  },

  async createAppointment(data: Partial<PayloadAppointment>): Promise<ApiResponse<PayloadAppointment>> {
    return payloadApi.post<PayloadAppointment>('/appointments', data);
  },

  async updateAppointment(id: string, data: Partial<PayloadAppointment>): Promise<ApiResponse<PayloadAppointment>> {
    return payloadApi.patch<PayloadAppointment>(`/appointments/${id}`, data);
  },

  // Blog Services
  async getBlogPosts(params?: {
    limit?: number;
    page?: number;
    category?: string;
    language?: string;
    isPublished?: boolean;
    isFeatured?: boolean;
  }): Promise<ApiResponse<PaginatedResponse<PayloadBlogPost>>> {
    const queryParams = {
      limit: params?.limit || 20,
      page: params?.page || 1,
      ...(params?.category && { category: params.category }),
      ...(params?.language && { language: params.language }),
      ...(params?.isPublished !== undefined && { isPublished: params.isPublished }),
      ...(params?.isFeatured !== undefined && { isFeatured: params.isFeatured }),
    };

    return payloadApi.get<PaginatedResponse<PayloadBlogPost>>('/blog-posts', queryParams);
  },

  async getBlogPost(id: string): Promise<ApiResponse<PayloadBlogPost>> {
    return payloadApi.get<PayloadBlogPost>(`/blog-posts/${id}`);
  },

  async getBlogPostBySlug(slug: string): Promise<ApiResponse<PayloadBlogPost>> {
    return payloadApi.get<PayloadBlogPost>(`/blog-posts`, { slug });
  },

  // Media Services
  async getMedia(params?: {
    limit?: number;
    page?: number;
    mimeType?: string;
  }): Promise<ApiResponse<PaginatedResponse<PayloadMedia>>> {
    const queryParams = {
      limit: params?.limit || 50,
      page: params?.page || 1,
      ...(params?.mimeType && { mimeType: params.mimeType }),
    };

    return payloadApi.get<PaginatedResponse<PayloadMedia>>('/media', queryParams);
  },

  async uploadMedia(file: File, alt?: string): Promise<ApiResponse<PayloadMedia>> {
    const formData = new FormData();
    formData.append('file', file);
    if (alt) formData.append('alt', alt);

    // Note: This will need special handling for file uploads
    return payloadApi.post<PayloadMedia>('/media', formData);
  },

  // User Services
  async getUsers(params?: {
    limit?: number;
    page?: number;
    role?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<PaginatedResponse<PayloadUser>>> {
    const queryParams = {
      limit: params?.limit || 50,
      page: params?.page || 1,
      ...(params?.role && { role: params.role }),
      ...(params?.isActive !== undefined && { isActive: params.isActive }),
    };

    return payloadApi.get<PaginatedResponse<PayloadUser>>('/users', queryParams);
  },

  // Health Check
  async healthCheck(): Promise<boolean> {
    return payloadApi.healthCheck();
  },
};

export default payloadContentService;
