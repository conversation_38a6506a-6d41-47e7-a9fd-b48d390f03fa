/**
 * Lullaby Clinic - Payload CMS API Integration
 * Service layer for communicating with Payload CMS backend
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import type { ApiResponse, PaginatedResponse } from '@/types/api';

// Backend API Configuration
export const PAYLOAD_CONFIG = {
  // Default to localhost for development, can be overridden by env vars
  API_URL: import.meta.env.VITE_PAYLOAD_API_URL || 'http://localhost:3000',
  REST_ENDPOINT: '/api',
  GRAPHQL_ENDPOINT: '/api/graphql',
  TIMEOUT: 10000,
  RETRIES: 3,
} as const;

// API Client Configuration
interface PayloadApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  headers: Record<string, string>;
}

class PayloadApiClient {
  private config: PayloadApiConfig;

  constructor() {
    this.config = {
      baseUrl: PAYLOAD_CONFIG.API_URL,
      timeout: PAYLOAD_CONFIG.TIMEOUT,
      retries: PAYLOAD_CONFIG.RETRIES,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      }
    };

    // Add API key if available
    const apiKey = import.meta.env.VITE_PAYLOAD_API_KEY;
    if (apiKey) {
      this.config.headers['Authorization'] = `Bearer ${apiKey}`;
    }
  }

  /**
   * Make HTTP request to Payload CMS API
   */
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.config.baseUrl}${PAYLOAD_CONFIG.REST_ENDPOINT}${endpoint}`;
    
    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...this.config.headers,
        ...options.headers,
      },
      signal: AbortSignal.timeout(this.config.timeout),
    };

    try {
      const response = await fetch(url, requestOptions);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: true,
        data,
        error: null,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Payload API request failed:', error);
      
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const searchParams = params ? new URLSearchParams(params).toString() : '';
    const url = searchParams ? `${endpoint}?${searchParams}` : endpoint;
    
    return this.request<T>(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  /**
   * GraphQL request
   */
  async graphql<T>(query: string, variables?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = `${this.config.baseUrl}${PAYLOAD_CONFIG.GRAPHQL_ENDPOINT}`;
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: this.config.headers,
        body: JSON.stringify({ query, variables }),
        signal: AbortSignal.timeout(this.config.timeout),
      });

      if (!response.ok) {
        throw new Error(`GraphQL HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(`GraphQL Error: ${result.errors.map((e: any) => e.message).join(', ')}`);
      }

      return {
        success: true,
        data: result.data,
        error: null,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('GraphQL request failed:', error);
      
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const payloadApi = new PayloadApiClient();

// Export types for external use
export type { PayloadApiConfig };
