/**
 * Lullaby Clinic - Payload CMS Hooks
 * React hooks for Payload CMS integration
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { payloadContentService } from '@/lib/payload-services';
import type { 
  PayloadDoctor, 
  PayloadService, 
  PayloadAppointment, 
  PayloadBlogPost,
  PayloadMedia,
  PayloadUser 
} from '@/lib/payload-services';

// Query Keys
export const PAYLOAD_QUERY_KEYS = {
  doctors: ['payload', 'doctors'] as const,
  doctor: (id: string) => ['payload', 'doctors', id] as const,
  services: ['payload', 'services'] as const,
  service: (id: string) => ['payload', 'services', id] as const,
  appointments: ['payload', 'appointments'] as const,
  appointment: (id: string) => ['payload', 'appointments', id] as const,
  blogPosts: ['payload', 'blog-posts'] as const,
  blogPost: (id: string) => ['payload', 'blog-posts', id] as const,
  blogPostBySlug: (slug: string) => ['payload', 'blog-posts', 'slug', slug] as const,
  media: ['payload', 'media'] as const,
  users: ['payload', 'users'] as const,
  health: ['payload', 'health'] as const,
} as const;

// Doctor Hooks
export const usePayloadDoctors = (params?: {
  limit?: number;
  page?: number;
  specialty?: string;
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: [...PAYLOAD_QUERY_KEYS.doctors, params],
    queryFn: () => payloadContentService.getDoctors(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => data.success ? data.data : null,
  });
};

export const usePayloadDoctor = (id: string) => {
  return useQuery({
    queryKey: PAYLOAD_QUERY_KEYS.doctor(id),
    queryFn: () => payloadContentService.getDoctor(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => data.success ? data.data : null,
  });
};

// Service Hooks
export const usePayloadServices = (params?: {
  limit?: number;
  page?: number;
  category?: string;
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: [...PAYLOAD_QUERY_KEYS.services, params],
    queryFn: () => payloadContentService.getServices(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => data.success ? data.data : null,
  });
};

export const usePayloadService = (id: string) => {
  return useQuery({
    queryKey: PAYLOAD_QUERY_KEYS.service(id),
    queryFn: () => payloadContentService.getService(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => data.success ? data.data : null,
  });
};

// Appointment Hooks
export const usePayloadAppointments = (params?: {
  limit?: number;
  page?: number;
  doctorId?: string;
  patientId?: string;
  status?: string;
  date?: string;
}) => {
  return useQuery({
    queryKey: [...PAYLOAD_QUERY_KEYS.appointments, params],
    queryFn: () => payloadContentService.getAppointments(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    select: (data) => data.success ? data.data : null,
  });
};

export const useCreatePayloadAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<PayloadAppointment>) => 
      payloadContentService.createAppointment(data),
    onSuccess: () => {
      // Invalidate appointments queries
      queryClient.invalidateQueries({ queryKey: PAYLOAD_QUERY_KEYS.appointments });
    },
  });
};

export const useUpdatePayloadAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<PayloadAppointment> }) =>
      payloadContentService.updateAppointment(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific appointment and appointments list
      queryClient.invalidateQueries({ queryKey: PAYLOAD_QUERY_KEYS.appointment(id) });
      queryClient.invalidateQueries({ queryKey: PAYLOAD_QUERY_KEYS.appointments });
    },
  });
};

// Blog Hooks
export const usePayloadBlogPosts = (params?: {
  limit?: number;
  page?: number;
  category?: string;
  language?: string;
  isPublished?: boolean;
  isFeatured?: boolean;
}) => {
  return useQuery({
    queryKey: [...PAYLOAD_QUERY_KEYS.blogPosts, params],
    queryFn: () => payloadContentService.getBlogPosts(params),
    staleTime: 15 * 60 * 1000, // 15 minutes
    select: (data) => data.success ? data.data : null,
  });
};

export const usePayloadBlogPost = (id: string) => {
  return useQuery({
    queryKey: PAYLOAD_QUERY_KEYS.blogPost(id),
    queryFn: () => payloadContentService.getBlogPost(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    select: (data) => data.success ? data.data : null,
  });
};

export const usePayloadBlogPostBySlug = (slug: string) => {
  return useQuery({
    queryKey: PAYLOAD_QUERY_KEYS.blogPostBySlug(slug),
    queryFn: () => payloadContentService.getBlogPostBySlug(slug),
    enabled: !!slug,
    staleTime: 15 * 60 * 1000, // 15 minutes
    select: (data) => data.success ? data.data : null,
  });
};

// Media Hooks
export const usePayloadMedia = (params?: {
  limit?: number;
  page?: number;
  mimeType?: string;
}) => {
  return useQuery({
    queryKey: [...PAYLOAD_QUERY_KEYS.media, params],
    queryFn: () => payloadContentService.getMedia(params),
    staleTime: 30 * 60 * 1000, // 30 minutes
    select: (data) => data.success ? data.data : null,
  });
};

export const useUploadPayloadMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, alt }: { file: File; alt?: string }) =>
      payloadContentService.uploadMedia(file, alt),
    onSuccess: () => {
      // Invalidate media queries
      queryClient.invalidateQueries({ queryKey: PAYLOAD_QUERY_KEYS.media });
    },
  });
};

// User Hooks
export const usePayloadUsers = (params?: {
  limit?: number;
  page?: number;
  role?: string;
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: [...PAYLOAD_QUERY_KEYS.users, params],
    queryFn: () => payloadContentService.getUsers(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => data.success ? data.data : null,
  });
};

// Health Check Hook
export const usePayloadHealthCheck = () => {
  return useQuery({
    queryKey: PAYLOAD_QUERY_KEYS.health,
    queryFn: () => payloadContentService.healthCheck(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Check every minute
    retry: 1,
  });
};

// Combined hook for checking backend connectivity
export const useBackendStatus = () => {
  const { data: isHealthy, isLoading, error } = usePayloadHealthCheck();
  
  return {
    isConnected: isHealthy === true,
    isLoading,
    error,
    status: isHealthy ? 'connected' : 'disconnected',
  };
};

// Export all hooks
export {
  payloadContentService,
  type PayloadDoctor,
  type PayloadService,
  type PayloadAppointment,
  type PayloadBlogPost,
  type PayloadMedia,
  type PayloadUser,
};
