/**
 * Lullaby Clinic - Enhanced Authentication Hook
 * React hook for integrated Supabase + Payload CMS authentication
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { authIntegration } from '@/lib/auth-integration';
import type { User, Session } from '@supabase/supabase-js';
import type { UserProfile } from '@/types/database';

// Enhanced auth state interface
interface EnhancedAuthState {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  payloadUser: any;
  permissions: string[];
  role: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isDoctor: boolean;
  isReceptionist: boolean;
  isPatient: boolean;
}

// Query keys
const AUTH_QUERY_KEYS = {
  enhancedSession: ['auth', 'enhanced-session'] as const,
  permissions: (email: string) => ['auth', 'permissions', email] as const,
} as const;

/**
 * Enhanced authentication hook with Payload CMS integration
 */
export const useEnhancedAuth = () => {
  const [authState, setAuthState] = useState<Partial<EnhancedAuthState>>({
    isLoading: true,
    isAuthenticated: false,
  });
  
  const queryClient = useQueryClient();

  // Query for enhanced session data
  const {
    data: sessionData,
    isLoading: isSessionLoading,
    error: sessionError,
    refetch: refetchSession,
  } = useQuery({
    queryKey: AUTH_QUERY_KEYS.enhancedSession,
    queryFn: () => authIntegration.getEnhancedSession(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Update auth state when session data changes
  useEffect(() => {
    if (sessionData) {
      const isAuthenticated = !!sessionData.session && !!sessionData.user;
      const role = sessionData.role;

      setAuthState({
        session: sessionData.session,
        user: sessionData.user,
        profile: sessionData.profile,
        payloadUser: sessionData.payloadUser,
        permissions: sessionData.permissions,
        role,
        isLoading: isSessionLoading,
        isAuthenticated,
        isAdmin: role === 'admin',
        isDoctor: role === 'doctor',
        isReceptionist: role === 'receptionist',
        isPatient: role === 'patient',
      });
    } else {
      setAuthState({
        session: null,
        user: null,
        profile: null,
        payloadUser: null,
        permissions: [],
        role: null,
        isLoading: isSessionLoading,
        isAuthenticated: false,
        isAdmin: false,
        isDoctor: false,
        isReceptionist: false,
        isPatient: false,
      });
    }
  }, [sessionData, isSessionLoading]);

  // Set up auth state change listener
  useEffect(() => {
    const { data: { subscription } } = authIntegration.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        // Invalidate and refetch session data
        queryClient.invalidateQueries({ queryKey: AUTH_QUERY_KEYS.enhancedSession });
        
        // Clear permissions cache if user signed out
        if (event === 'SIGNED_OUT') {
          queryClient.removeQueries({ queryKey: ['auth', 'permissions'] });
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient]);

  // Permission checking functions
  const hasPermission = useCallback((permission: string): boolean => {
    return authState.permissions?.includes(permission) || false;
  }, [authState.permissions]);

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  }, [hasPermission]);

  const hasAllPermissions = useCallback((permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  }, [hasPermission]);

  // Role checking functions
  const hasRole = useCallback((role: string): boolean => {
    return authState.role === role;
  }, [authState.role]);

  const hasAnyRole = useCallback((roles: string[]): boolean => {
    return roles.includes(authState.role || '');
  }, [authState.role]);

  // Admin/staff checking
  const isStaff = useCallback((): boolean => {
    return hasAnyRole(['admin', 'doctor', 'receptionist']);
  }, [hasAnyRole]);

  const canManageAppointments = useCallback((): boolean => {
    return hasAnyPermission(['manage_appointments', 'manage_own_appointments']);
  }, [hasAnyPermission]);

  const canViewPatients = useCallback((): boolean => {
    return hasAnyPermission(['view_patients', 'manage_patient_info']);
  }, [hasAnyPermission]);

  const canManageContent = useCallback((): boolean => {
    return hasPermission('manage_content');
  }, [hasPermission]);

  // Sign out function
  const signOut = useCallback(async () => {
    try {
      await authIntegration.signOut();
      // Clear all auth-related queries
      queryClient.removeQueries({ queryKey: ['auth'] });
    } catch (error) {
      console.error('Sign out failed:', error);
      throw error;
    }
  }, [queryClient]);

  // Refresh session
  const refreshSession = useCallback(() => {
    return refetchSession();
  }, [refetchSession]);

  return {
    // Auth state
    ...authState,
    isLoading: isSessionLoading,
    error: sessionError,

    // Permission functions
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,

    // Role functions
    hasRole,
    hasAnyRole,
    isStaff,

    // Convenience functions
    canManageAppointments,
    canViewPatients,
    canManageContent,

    // Actions
    signOut,
    refreshSession,
  } as EnhancedAuthState & {
    error: any;
    hasPermission: (permission: string) => boolean;
    hasAnyPermission: (permissions: string[]) => boolean;
    hasAllPermissions: (permissions: string[]) => boolean;
    hasRole: (role: string) => boolean;
    hasAnyRole: (roles: string[]) => boolean;
    isStaff: () => boolean;
    canManageAppointments: () => boolean;
    canViewPatients: () => boolean;
    canManageContent: () => boolean;
    signOut: () => Promise<void>;
    refreshSession: () => Promise<any>;
  };
};

/**
 * Hook for checking specific permissions
 */
export const usePermissions = (permissions: string | string[]) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, isLoading } = useEnhancedAuth();

  const permissionArray = Array.isArray(permissions) ? permissions : [permissions];

  return {
    hasPermission: Array.isArray(permissions) 
      ? hasAnyPermission(permissions)
      : hasPermission(permissions),
    hasAnyPermission: hasAnyPermission(permissionArray),
    hasAllPermissions: hasAllPermissions(permissionArray),
    isLoading,
  };
};

/**
 * Hook for role-based access control
 */
export const useRoleAccess = (allowedRoles: string | string[]) => {
  const { role, hasRole, hasAnyRole, isLoading, isAuthenticated } = useEnhancedAuth();

  const roleArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

  return {
    hasAccess: isAuthenticated && (
      Array.isArray(allowedRoles) 
        ? hasAnyRole(allowedRoles)
        : hasRole(allowedRoles)
    ),
    currentRole: role,
    isLoading,
    isAuthenticated,
  };
};

export default useEnhancedAuth;
