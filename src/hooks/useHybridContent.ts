/**
 * Lullaby Clinic - Hybrid Content Hooks
 * React hooks that use both Supabase and Payload CMS
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { useQuery } from '@tanstack/react-query';
import { hybridDoctorService, hybridServiceService, hybridContentService, hybridHealthService } from '@/lib/hybrid-services';

// Query Keys for hybrid services
export const HYBRID_QUERY_KEYS = {
  doctors: ['hybrid', 'doctors'] as const,
  doctor: (id: string) => ['hybrid', 'doctors', id] as const,
  services: ['hybrid', 'services'] as const,
  service: (id: string) => ['hybrid', 'services', id] as const,
  blogPosts: ['hybrid', 'blog-posts'] as const,
  blogPost: (id: string) => ['hybrid', 'blog-posts', id] as const,
  systemHealth: ['hybrid', 'health'] as const,
} as const;

// Doctor Hooks
export const useHybridDoctors = (filters?: { 
  specialty?: string; 
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: [...HYBRID_QUERY_KEYS.doctors, filters],
    queryFn: () => hybridDoctorService.getDoctors(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => ({
      doctors: data.success ? data.data : [],
      source: data.source,
      error: data.error,
    }),
  });
};

export const useHybridDoctor = (id: string) => {
  return useQuery({
    queryKey: HYBRID_QUERY_KEYS.doctor(id),
    queryFn: () => hybridDoctorService.getDoctor(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => ({
      doctor: data.success ? data.data : null,
      source: data.source,
      error: data.error,
    }),
  });
};

// Service Hooks
export const useHybridServices = (filters?: { 
  category?: string; 
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: [...HYBRID_QUERY_KEYS.services, filters],
    queryFn: () => hybridServiceService.getServices(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => ({
      services: data.success ? data.data : [],
      source: data.source,
      error: data.error,
    }),
  });
};

export const useHybridService = (id: string) => {
  return useQuery({
    queryKey: HYBRID_QUERY_KEYS.service(id),
    queryFn: () => hybridServiceService.getService(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => ({
      service: data.success ? data.data : null,
      source: data.source,
      error: data.error,
    }),
  });
};

// Content Hooks
export const useHybridBlogPosts = (filters?: {
  category?: string;
  language?: string;
  featured?: boolean;
  limit?: number;
}) => {
  return useQuery({
    queryKey: [...HYBRID_QUERY_KEYS.blogPosts, filters],
    queryFn: () => hybridContentService.getBlogPosts(filters),
    staleTime: 15 * 60 * 1000, // 15 minutes
    select: (data) => ({
      posts: data.success ? data.data : [],
      source: data.source,
      error: data.error,
    }),
  });
};

export const useHybridBlogPost = (id: string) => {
  return useQuery({
    queryKey: HYBRID_QUERY_KEYS.blogPost(id),
    queryFn: () => hybridContentService.getBlogPost(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    select: (data) => ({
      post: data.success ? data.data : null,
      source: data.source,
      error: data.error,
    }),
  });
};

// System Health Hook
export const useSystemHealth = () => {
  return useQuery({
    queryKey: HYBRID_QUERY_KEYS.systemHealth,
    queryFn: () => hybridHealthService.checkSystemHealth(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Check every minute
    retry: 1,
  });
};

// Combined hook for backend status with user-friendly information
export const useBackendIntegrationStatus = () => {
  const { data: health, isLoading, error } = useSystemHealth();
  
  const getStatusMessage = () => {
    if (isLoading) return 'Checking backend systems...';
    if (error) return 'Unable to check backend status';
    if (!health) return 'Backend status unknown';
    
    const { payload, supabase, overall } = health;
    
    if (overall.status === 'healthy') {
      if (payload.available && supabase.available) {
        return 'All systems operational';
      } else if (payload.available) {
        return 'Content management system online';
      } else if (supabase.available) {
        return 'Database system online';
      }
    }
    
    return 'Some systems may be unavailable';
  };

  const getStatusColor = () => {
    if (isLoading) return 'yellow';
    if (error || !health) return 'red';
    
    const { overall } = health;
    return overall.status === 'healthy' ? 'green' : 'orange';
  };

  return {
    health,
    isLoading,
    error,
    statusMessage: getStatusMessage(),
    statusColor: getStatusColor(),
    isHealthy: health?.overall.status === 'healthy',
    systems: {
      payload: health?.payload || { status: 'unknown', available: false },
      supabase: health?.supabase || { status: 'unknown', available: false },
    },
  };
};

// Hook for checking if specific features are available
export const useFeatureAvailability = () => {
  const { health } = useSystemHealth();
  
  return {
    contentManagement: health?.payload.available || false,
    userAuthentication: health?.supabase.available || false,
    appointmentBooking: health?.supabase.available || false,
    blogPosts: health?.payload.available || health?.supabase.available || false,
    doctorProfiles: health?.payload.available || health?.supabase.available || false,
    servicesCatalog: health?.payload.available || health?.supabase.available || false,
  };
};

// Development helper hook for debugging data sources
export const useDataSourceDebug = () => {
  const doctorsQuery = useHybridDoctors();
  const servicesQuery = useHybridServices();
  const blogPostsQuery = useHybridBlogPosts();
  
  return {
    dataSources: {
      doctors: doctorsQuery.data?.source || 'unknown',
      services: servicesQuery.data?.source || 'unknown',
      blogPosts: blogPostsQuery.data?.source || 'unknown',
    },
    errors: {
      doctors: doctorsQuery.data?.error,
      services: servicesQuery.data?.error,
      blogPosts: blogPostsQuery.data?.error,
    },
    isLoading: {
      doctors: doctorsQuery.isLoading,
      services: servicesQuery.isLoading,
      blogPosts: blogPostsQuery.isLoading,
    },
  };
};

// Export all hooks and utilities
export {
  hybridDoctorService,
  hybridServiceService,
  hybridContentService,
  hybridHealthService,
};
