
/**
 * Lullaby Clinic - Audit Type Definitions
 * Types for audit logging and compliance
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import type { AuditAction } from './common';

export interface AuditLog {
  id: string;
  user_id?: string;
  table_name: string;
  record_id: string;
  action: AuditAction;
  old_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface AuditLogInput {
  table_name: string;
  record_id: string;
  action: AuditAction;
  old_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  ip_address?: string;
  user_agent?: string;
}
