
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Routes, Route } from "react-router-dom";
import ErrorBoundary from "@/components/ErrorBoundary";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import AuthProvider from "@/contexts/AuthProvider";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

// Lazy load components for better performance
import { lazy, Suspense } from "react";

const Auth = lazy(() => import("@/pages/Auth"));
const LoginForm = lazy(() => import("@/components/auth/LoginForm"));
const SignupForm = lazy(() => import("@/components/auth/SignupForm"));
const ResetPasswordForm = lazy(() => import("@/components/auth/ResetPasswordForm"));
const AuthCallback = lazy(() => import("@/pages/AuthCallback"));
const VerifyEmail = lazy(() => import("@/pages/VerifyEmail"));
const PatientDashboard = lazy(() => import("@/components/PatientDashboard"));
const BookingSystem = lazy(() => import("@/components/BookingSystem"));
const About = lazy(() => import("@/pages/About"));
const Services = lazy(() => import("@/pages/Services"));
const Doctors = lazy(() => import("@/pages/Doctors"));
const Contact = lazy(() => import("@/pages/Contact"));
const Blog = lazy(() => import("@/pages/Blog"));
const Gallery = lazy(() => import("@/pages/Gallery"));
const Pricing = lazy(() => import("@/pages/Pricing"));
const Appointments = lazy(() => import("@/pages/Appointments"));
const Reviews = lazy(() => import("@/pages/Reviews"));

// Loading fallback component
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center space-y-4">
      <div className="w-8 h-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto" />
      <p className="text-gray-600">Loading...</p>
    </div>
  </div>
);

const App = () => (
  <ErrorBoundary level="critical" showDetails={import.meta.env.DEV}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <ErrorBoundary level="page">
          <Suspense fallback={<LoadingFallback />}>
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Index />} />
              <Route path="/:lang" element={<Index />} />
              
              {/* About page routes */}
              <Route path="/about" element={<About />} />
              <Route path="/:lang/about" element={<About />} />
              
              {/* Services page routes */}
              <Route path="/services" element={<Services />} />
              <Route path="/:lang/services" element={<Services />} />
              
              {/* Doctors page routes */}
              <Route path="/doctors" element={<Doctors />} />
              <Route path="/:lang/doctors" element={<Doctors />} />
              
              {/* Contact page routes */}
              <Route path="/contact" element={<Contact />} />
              <Route path="/:lang/contact" element={<Contact />} />
              
              {/* Blog page routes */}
              <Route path="/blog" element={<Blog />} />
              <Route path="/:lang/blog" element={<Blog />} />
              
              {/* Gallery page routes */}
              <Route path="/gallery" element={<Gallery />} />
              <Route path="/:lang/gallery" element={<Gallery />} />
              
              {/* Pricing page routes */}
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/:lang/pricing" element={<Pricing />} />
              
              {/* Appointments page routes */}
              <Route path="/appointments" element={<Appointments />} />
              <Route path="/:lang/appointments" element={<Appointments />} />
              
              {/* Reviews page routes */}
              <Route path="/reviews" element={<Reviews />} />
              <Route path="/:lang/reviews" element={<Reviews />} />
              
              {/* Authentication routes */}
              <Route path="/auth" element={<Auth />} />
              <Route path="/auth/login" element={<LoginForm />} />
              <Route path="/auth/signup" element={<SignupForm />} />
              <Route path="/auth/reset-password" element={<ResetPasswordForm />} />
              <Route path="/auth/callback" element={<AuthCallback />} />
              <Route path="/auth/verify-email" element={<VerifyEmail />} />
              
              {/* Booking system - accessible to both authenticated and guest users */}
              <Route path="/book-appointment" element={<BookingSystem />} />
              
              {/* Protected routes - require authentication */}
              <Route 
                path="/dashboard" 
                element={
                  <ProtectedRoute requireAuth={true}>
                    <PatientDashboard />
                  </ProtectedRoute>
                } 
              />
              
              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </ErrorBoundary>
      </TooltipProvider>
    </AuthProvider>
  </ErrorBoundary>
);

export default App;
