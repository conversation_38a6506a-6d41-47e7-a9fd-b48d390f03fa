
/**
 * Lullaby Clinic - Comprehensive Before/After Gallery
 * Advanced gallery with image comparison, filtering, and animations
 *
 * @version 2.0.0
 * @created 2024-06-24
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Filter, X, Play, Pause, Eye, Star, Camera, ZoomIn } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import LazyImage from '@/components/LazyImage';

interface BeforeAfterImage {
  id: string;
  before: string;
  after: string;
  title: string;
  category: string;
  treatment: string;
  duration: string;
  description: string;
  patientAge?: number;
  patientGender?: 'male' | 'female';
  difficulty: 'easy' | 'moderate' | 'complex';
  rating: number;
  featured: boolean;
  tags: string[];
}

interface BeforeAfterGalleryProps {
  translations: any;
  className?: string;
}

// Sample data - in real app, this would come from your backend
const sampleGalleryData: BeforeAfterImage[] = [
  {
    id: '1',
    before: '/placeholder.svg',
    after: '/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png',
    title: 'Acne Treatment Transformation',
    category: 'acne',
    treatment: 'Laser + Chemical Peel',
    duration: '3 months',
    description: 'Complete acne treatment with laser therapy and chemical peels for clear, smooth skin.',
    patientAge: 25,
    patientGender: 'female',
    difficulty: 'moderate',
    rating: 4.8,
    featured: true,
    tags: ['laser', 'chemical-peel', 'acne-scars']
  },
  {
    id: '2',
    before: '/placeholder.svg',
    after: '/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png',
    title: 'Pigmentation Correction',
    category: 'pigmentation',
    treatment: 'IPL + Vitamin C',
    duration: '2 months',
    description: 'Advanced IPL treatment combined with vitamin C therapy for even skin tone.',
    patientAge: 35,
    patientGender: 'female',
    difficulty: 'easy',
    rating: 4.9,
    featured: true,
    tags: ['ipl', 'vitamin-c', 'melasma']
  },
  {
    id: '3',
    before: '/placeholder.svg',
    after: '/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png',
    title: 'Anti-Aging Treatment',
    category: 'anti-aging',
    treatment: 'Botox + Dermal Fillers',
    duration: '1 session',
    description: 'Non-surgical facial rejuvenation with botox and dermal fillers.',
    patientAge: 45,
    patientGender: 'female',
    difficulty: 'complex',
    rating: 5.0,
    featured: false,
    tags: ['botox', 'fillers', 'wrinkles']
  },
  {
    id: '4',
    before: '/placeholder.svg',
    after: '/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png',
    title: 'Scar Reduction',
    category: 'scars',
    treatment: 'Fractional Laser',
    duration: '4 months',
    description: 'Fractional laser treatment for significant scar reduction and skin texture improvement.',
    patientAge: 28,
    patientGender: 'male',
    difficulty: 'complex',
    rating: 4.7,
    featured: false,
    tags: ['fractional-laser', 'acne-scars', 'texture']
  },
  {
    id: '5',
    before: '/placeholder.svg',
    after: '/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png',
    title: 'Dark Spot Removal',
    category: 'pigmentation',
    treatment: 'Q-Switch Laser',
    duration: '6 weeks',
    description: 'Targeted Q-Switch laser treatment for precise dark spot removal.',
    patientAge: 40,
    patientGender: 'female',
    difficulty: 'easy',
    rating: 4.6,
    featured: true,
    tags: ['q-switch', 'dark-spots', 'sun-damage']
  },
  {
    id: '6',
    before: '/placeholder.svg',
    after: '/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png',
    title: 'Pore Minimization',
    category: 'pores',
    treatment: 'Microneedling + PRP',
    duration: '3 months',
    description: 'Advanced microneedling with PRP for pore reduction and skin refinement.',
    patientAge: 30,
    patientGender: 'female',
    difficulty: 'moderate',
    rating: 4.5,
    featured: false,
    tags: ['microneedling', 'prp', 'pore-reduction']
  }
];

const categories = [
  { id: 'all', name: 'All Treatments', icon: '🌟' },
  { id: 'acne', name: 'Acne Treatment', icon: '✨' },
  { id: 'pigmentation', name: 'Pigmentation', icon: '🎯' },
  { id: 'anti-aging', name: 'Anti-Aging', icon: '⏰' },
  { id: 'scars', name: 'Scar Treatment', icon: '🔄' },
  { id: 'pores', name: 'Pore Treatment', icon: '💎' }
];

export const BeforeAfterGallery: React.FC<BeforeAfterGalleryProps> = ({
  translations,
  className = ''
}) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedImage, setSelectedImage] = useState<BeforeAfterImage | null>(null);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'rating' | 'featured' | 'recent'>('featured');

  const autoPlayRef = useRef<NodeJS.Timeout>();
  const galleryRef = useRef<HTMLDivElement>(null);

  // Filter and sort images
  const filteredImages = sampleGalleryData
    .filter(image => selectedCategory === 'all' || image.category === selectedCategory)
    .sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'featured':
          return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
        case 'recent':
          return b.id.localeCompare(a.id); // Assuming higher ID = more recent
        default:
          return 0;
      }
    });

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlay && filteredImages.length > 0) {
      autoPlayRef.current = setInterval(() => {
        setCurrentSlide(prev => (prev + 1) % filteredImages.length);
      }, 4000);
    } else {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlay, filteredImages.length]);

  // Reset slide when category changes
  useEffect(() => {
    setCurrentSlide(0);
  }, [selectedCategory]);

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % filteredImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + filteredImages.length) % filteredImages.length);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'complex': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < Math.floor(rating)
            ? 'fill-yellow-400 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section className={`py-16 bg-gradient-to-br from-pink-50 to-purple-50 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {translations?.beforeAfter?.title || 'Before & After Gallery'}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {translations?.beforeAfter?.subtitle || 'Witness the remarkable transformations achieved through our advanced treatments and expert care.'}
          </p>

          {/* Controls */}
          <div className="flex flex-wrap justify-center items-center gap-4 mb-8">
            <Button
              variant={showFilters ? "default" : "outline"}
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>

            <Button
              variant={isAutoPlay ? "default" : "outline"}
              onClick={() => setIsAutoPlay(!isAutoPlay)}
              className="flex items-center gap-2"
            >
              {isAutoPlay ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              {isAutoPlay ? 'Pause' : 'Auto Play'}
            </Button>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="border rounded px-2 py-1 text-sm"
              >
                <option value="featured">Featured</option>
                <option value="rating">Rating</option>
                <option value="recent">Recent</option>
              </select>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8 max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                {categories.map(category => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category.id)}
                    className="flex flex-col items-center gap-1 h-auto py-3"
                  >
                    <span className="text-lg">{category.icon}</span>
                    <span className="text-xs">{category.name}</span>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Main Gallery */}
        <div className="relative">
          {/* Featured Carousel */}
          <div className="mb-12">
            <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl">
              <div
                ref={galleryRef}
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {filteredImages.map((image, index) => (
                  <div key={image.id} className="w-full flex-shrink-0">
                    <BeforeAfterSlider
                      image={image}
                      onViewDetails={() => setSelectedImage(image)}
                    />
                  </div>
                ))}
              </div>

              {/* Navigation Arrows */}
              {filteredImages.length > 1 && (
                <>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={prevSlide}
                    className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={nextSlide}
                    className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Slide Indicators */}
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                {filteredImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentSlide ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Grid Gallery */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredImages.map((image) => (
              <Card
                key={image.id}
                className="group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                onClick={() => setSelectedImage(image)}
              >
                <CardContent className="p-0">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <LazyImage
                      src={image.after}
                      alt={image.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                      threshold={0.1}
                      rootMargin="50px"
                    />
                    {image.featured && (
                      <Badge className="absolute top-2 left-2 bg-gradient-to-r from-pink-500 to-purple-500">
                        Featured
                      </Badge>
                    )}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                      <Eye className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </div>

                  <div className="p-4">
                    <h3 className="font-semibold text-lg mb-2 group-hover:text-pink-600 transition-colors">
                      {image.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {image.description}
                    </p>

                    <div className="flex items-center justify-between mb-3">
                      <Badge variant="outline" className={getDifficultyColor(image.difficulty)}>
                        {image.difficulty}
                      </Badge>
                      <div className="flex items-center gap-1">
                        {getRatingStars(image.rating)}
                        <span className="text-sm text-gray-600 ml-1">
                          {image.rating}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{image.treatment}</span>
                      <span>{image.duration}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Results Summary */}
        <div className="mt-12 text-center">
          <p className="text-gray-600">
            Showing {filteredImages.length} results
            {selectedCategory !== 'all' && ` in ${categories.find(c => c.id === selectedCategory)?.name}`}
          </p>
        </div>
      </div>

      {/* Detail Modal */}
      {selectedImage && (
        <ImageDetailModal
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
          translations={translations}
        />
      )}
    </section>
  );
};

// Before/After Slider Component with drag functionality
interface BeforeAfterSliderProps {
  image: BeforeAfterImage;
  onViewDetails: () => void;
}

const BeforeAfterSlider: React.FC<BeforeAfterSliderProps> = ({ image, onViewDetails }) => {
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    updateSliderPosition(e.clientX);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      updateSliderPosition(e.clientX);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const updateSliderPosition = (clientX: number) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const position = ((clientX - rect.left) / rect.width) * 100;
      setSliderPosition(Math.max(0, Math.min(100, position)));
    }
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  return (
    <div
      ref={containerRef}
      className="relative h-96 overflow-hidden cursor-col-resize select-none"
      onMouseDown={handleMouseDown}
    >
      {/* Before Image */}
      <LazyImage
        src={image.before}
        alt={`Before ${image.title}`}
        className="absolute inset-0 w-full h-full object-cover"
        loading="eager"
        priority={true}
      />

      {/* After Image */}
      <div
        className="absolute inset-0 overflow-hidden"
        style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
      >
        <LazyImage
          src={image.after}
          alt={`After ${image.title}`}
          className="w-full h-full object-cover"
          loading="eager"
          priority={true}
        />
      </div>

      {/* Slider Line */}
      <div
        className="absolute top-0 bottom-0 w-1 bg-white shadow-lg z-10"
        style={{ left: `${sliderPosition}%` }}
      >
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
          <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Labels */}
      <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
        Before
      </div>
      <div className="absolute top-4 right-4 bg-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium">
        After
      </div>

      {/* Content Overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 text-white">
        <h3 className="text-2xl font-bold mb-2">{image.title}</h3>
        <p className="text-sm opacity-90 mb-3">{image.description}</p>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm">
            <span>{image.treatment}</span>
            <span>•</span>
            <span>{image.duration}</span>
          </div>
          <Button
            onClick={(e) => {
              e.stopPropagation();
              onViewDetails();
            }}
            variant="secondary"
            size="sm"
            className="bg-white/20 hover:bg-white/30 text-white border-white/30"
          >
            View Details
          </Button>
        </div>
      </div>
    </div>
  );
};

// Image Detail Modal Component
interface ImageDetailModalProps {
  image: BeforeAfterImage;
  onClose: () => void;
  translations: any;
}

const ImageDetailModal: React.FC<ImageDetailModalProps> = ({ image, onClose, translations }) => {
  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">{image.title}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="comparison" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="comparison">Comparison</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="treatment">Treatment</TabsTrigger>
          </TabsList>

          <TabsContent value="comparison" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-semibold mb-3 text-center">Before</h4>
                <LazyImage
                  src={image.before}
                  alt={`Before ${image.title}`}
                  className="w-full h-80 object-cover rounded-lg"
                  loading="eager"
                  priority={true}
                />
              </div>
              <div>
                <h4 className="text-lg font-semibold mb-3 text-center">After</h4>
                <LazyImage
                  src={image.after}
                  alt={`After ${image.title}`}
                  className="w-full h-80 object-cover rounded-lg"
                  loading="eager"
                  priority={true}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="text-lg font-semibold">Patient Information</h4>
                <div className="space-y-2">
                  {image.patientAge && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Age:</span>
                      <span>{image.patientAge} years</span>
                    </div>
                  )}
                  {image.patientGender && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Gender:</span>
                      <span className="capitalize">{image.patientGender}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category:</span>
                    <span className="capitalize">{image.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Difficulty:</span>
                    <Badge className={getDifficultyColor(image.difficulty)}>
                      {image.difficulty}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-lg font-semibold">Treatment Results</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span>{image.duration}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Rating:</span>
                    <div className="flex items-center gap-1">
                      {getRatingStars(image.rating)}
                      <span className="ml-1">{image.rating}/5</span>
                    </div>
                  </div>
                  {image.featured && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <Badge className="bg-gradient-to-r from-pink-500 to-purple-500">
                        Featured Case
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-3">Description</h4>
              <p className="text-gray-600 leading-relaxed">{image.description}</p>
            </div>

            {image.tags.length > 0 && (
              <div>
                <h4 className="text-lg font-semibold mb-3">Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {image.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="treatment" className="space-y-4">
            <div>
              <h4 className="text-lg font-semibold mb-3">Treatment Details</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium mb-2">{image.treatment}</h5>
                <p className="text-gray-600 text-sm mb-4">{image.description}</p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Duration:</span>
                    <p className="text-gray-600">{image.duration}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Difficulty:</span>
                    <p className="text-gray-600 capitalize">{image.difficulty}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Category:</span>
                    <p className="text-gray-600 capitalize">{image.category}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-4">
              <h5 className="font-medium text-blue-900 mb-2">Interested in this treatment?</h5>
              <p className="text-blue-700 text-sm mb-3">
                Book a consultation to discuss how this treatment can help you achieve similar results.
              </p>
              <Button className="bg-blue-600 hover:bg-blue-700">
                Book Consultation
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'easy': return 'bg-green-100 text-green-800';
    case 'moderate': return 'bg-yellow-100 text-yellow-800';
    case 'complex': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getRatingStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, i) => (
    <Star
      key={i}
      className={`h-3 w-3 ${
        i < Math.floor(rating)
          ? 'fill-yellow-400 text-yellow-400'
          : 'text-gray-300'
      }`}
    />
  ));
};

export default BeforeAfterGallery;
