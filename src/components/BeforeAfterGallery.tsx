
import React, { useState } from 'react';
import { Camera, ZoomIn, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  <PERSON>alog,
  DialogContent,
  DialogTrigger,
} from '@/components/ui/dialog';
import LazyImage from '@/components/LazyImage';

interface BeforeAfterGalleryProps {
  translations: {
    gallery: {
      badge: string;
      title: string;
      subtitle: string;
      before: string;
      after: string;
      viewMore: string;
      item1: { treatment: string; description: string };
      item2: { treatment: string; description: string };
      item3: { treatment: string; description: string };
      item4: { treatment: string; description: string };
      item5: { treatment: string; description: string };
      item6: { treatment: string; description: string };
    };
  };
}

const BeforeAfterGallery = ({ translations }: BeforeAfterGalleryProps) => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const galleryItems = [
    {
      before: "/placeholder.svg",
      after: "/placeholder.svg",
      treatment: translations.gallery.item1.treatment,
      description: translations.gallery.item1.description
    },
    {
      before: "/placeholder.svg",
      after: "/placeholder.svg",
      treatment: translations.gallery.item2.treatment,
      description: translations.gallery.item2.description
    },
    {
      before: "/placeholder.svg",
      after: "/placeholder.svg",
      treatment: translations.gallery.item3.treatment,
      description: translations.gallery.item3.description
    },
    {
      before: "/placeholder.svg",
      after: "/placeholder.svg",
      treatment: translations.gallery.item4.treatment,
      description: translations.gallery.item4.description
    },
    {
      before: "/placeholder.svg",
      after: "/placeholder.svg",
      treatment: translations.gallery.item5.treatment,
      description: translations.gallery.item5.description
    },
    {
      before: "/placeholder.svg",
      after: "/placeholder.svg",
      treatment: translations.gallery.item6.treatment,
      description: translations.gallery.item6.description
    }
  ];

  return (
    <section id="gallery" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Camera className="h-4 w-4 mr-2" />
            {translations.gallery.badge}
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {translations.gallery.title}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translations.gallery.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {galleryItems.map((item, index) => (
            <Card key={index} className="group overflow-hidden hover:shadow-soft transition-all duration-300">
              <div className="relative">
                <div className="grid grid-cols-2 h-64">
                  <div className="relative overflow-hidden">
                    <LazyImage 
                      src={item.before}
                      alt={`Before ${item.treatment}`}
                      className="w-full h-full"
                      objectFit="cover"
                      loading="lazy"
                      threshold={0.1}
                      rootMargin="50px"
                    />
                    <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                      {translations.gallery.before}
                    </div>
                  </div>
                  <div className="relative overflow-hidden">
                    <LazyImage 
                      src={item.after}
                      alt={`After ${item.treatment}`}
                      className="w-full h-full"
                      objectFit="cover"
                      loading="lazy"
                      threshold={0.1}
                      rootMargin="50px"
                    />
                    <div className="absolute bottom-2 right-2 bg-primary-500 text-white px-2 py-1 rounded text-xs">
                      {translations.gallery.after}
                    </div>
                  </div>
                </div>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button 
                      size="sm"
                      className="absolute top-2 right-2 bg-white/90 text-foreground hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-lg font-semibold mb-2">{translations.gallery.before}</h4>
                        <LazyImage 
                          src={item.before}
                          alt={`Before ${item.treatment}`}
                          className="w-full h-64 rounded-lg"
                          objectFit="cover"
                          loading="eager"
                          priority={true}
                        />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold mb-2">{translations.gallery.after}</h4>
                        <LazyImage 
                          src={item.after}
                          alt={`After ${item.treatment}`}
                          className="w-full h-64 rounded-lg"
                          objectFit="cover"
                          loading="eager"
                          priority={true}
                        />
                      </div>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-xl font-semibold text-foreground mb-2">
                        {item.treatment}
                      </h3>
                      <p className="text-muted-foreground">{item.description}</p>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
              
              <div className="p-4">
                <h3 className="font-semibold text-foreground mb-1">{item.treatment}</h3>
                <p className="text-sm text-muted-foreground">{item.description}</p>
              </div>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg" className="border-primary-200 text-primary-600 hover:bg-primary-50">
            {translations.gallery.viewMore}
            <Camera className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default BeforeAfterGallery;
