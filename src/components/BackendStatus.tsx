/**
 * Lullaby Clinic - Backend Status Component
 * Displays the status of backend integrations
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import React from 'react';
import { useBackendIntegrationStatus, useFeatureAvailability } from '@/hooks/useHybridContent';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader2,
  Database,
  Server,
  FileText,
  Users,
  Calendar,
  CreditCard
} from 'lucide-react';

interface BackendStatusProps {
  showDetails?: boolean;
  className?: string;
}

export const BackendStatus: React.FC<BackendStatusProps> = ({ 
  showDetails = false,
  className = '' 
}) => {
  const { 
    isHealthy, 
    statusMessage, 
    statusColor, 
    systems, 
    isLoading 
  } = useBackendIntegrationStatus();

  const features = useFeatureAvailability();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadgeVariant = (color: string) => {
    switch (color) {
      case 'green':
        return 'default';
      case 'red':
        return 'destructive';
      case 'yellow':
      case 'orange':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (!showDetails) {
    // Compact status indicator
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
        ) : (
          getStatusIcon(isHealthy ? 'healthy' : 'unhealthy')
        )}
        <Badge variant={getStatusBadgeVariant(statusColor)}>
          {statusMessage}
        </Badge>
      </div>
    );
  }

  // Detailed status view
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overall Status */}
      <Alert>
        <div className="flex items-center gap-2">
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            getStatusIcon(isHealthy ? 'healthy' : 'unhealthy')
          )}
          <AlertDescription className="font-medium">
            {statusMessage}
          </AlertDescription>
        </div>
      </Alert>

      {/* System Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Payload CMS Status */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Server className="h-4 w-4" />
              Payload CMS
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Content Management</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(systems.payload.status)}
                <span className="text-xs capitalize">{systems.payload.status}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Supabase Status */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Database className="h-4 w-4" />
              Supabase
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Database & Auth</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(systems.supabase.status)}
                <span className="text-xs capitalize">{systems.supabase.status}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feature Availability */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Feature Availability</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span className="text-xs">Content</span>
              {features.contentManagement ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <XCircle className="h-3 w-3 text-red-500" />
              )}
            </div>

            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="text-xs">Auth</span>
              {features.userAuthentication ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <XCircle className="h-3 w-3 text-red-500" />
              )}
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span className="text-xs">Booking</span>
              {features.appointmentBooking ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <XCircle className="h-3 w-3 text-red-500" />
              )}
            </div>

            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span className="text-xs">Blog</span>
              {features.blogPosts ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <XCircle className="h-3 w-3 text-red-500" />
              )}
            </div>

            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="text-xs">Doctors</span>
              {features.doctorProfiles ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <XCircle className="h-3 w-3 text-red-500" />
              )}
            </div>

            <div className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              <span className="text-xs">Services</span>
              {features.servicesCatalog ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <XCircle className="h-3 w-3 text-red-500" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Development Info */}
      {import.meta.env.DEV && (
        <Card className="border-dashed">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-600">Development Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-xs text-gray-500">
              <div>Payload API: {import.meta.env.VITE_PAYLOAD_API_URL || 'Not configured'}</div>
              <div>Supabase URL: {import.meta.env.VITE_SUPABASE_URL || 'Not configured'}</div>
              <div>Environment: {import.meta.env.MODE}</div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BackendStatus;
