
import React, { memo } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

const GlassCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: GlassCardProps) => (
  <Card 
    className={`
      backdrop-blur-md bg-white/10 border border-white/20 shadow-xl
      ${hover ? 'hover:bg-white/15 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
      backdropFilter: 'blur(16px)',
      WebkitBackdropFilter: 'blur(16px)',
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

GlassCard.displayName = 'GlassCard';

export default GlassCard;
