
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

const AboutApproach = () => {
  const { t } = useLanguage();

  return (
    <section>
      <GlassCard>
        <div className="text-center">
          <h2 className="text-3xl font-semibold text-white mb-6">{t('about.approach.title')}</h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            {t('about.approach.description')}
          </p>
        </div>
      </GlassCard>
    </section>
  );
};

export default AboutApproach;
