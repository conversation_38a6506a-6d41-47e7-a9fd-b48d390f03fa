
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

const MissionVision = () => {
  const { t } = useLanguage();

  return (
    <section className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <GlassCard hover className="group">
        <div className="text-4xl mb-4">🎯</div>
        <h2 className="text-2xl font-bold text-white mb-4">{t('about.mission.title')}</h2>
        <p className="text-white/80 leading-relaxed">{t('about.mission.description')}</p>
      </GlassCard>

      <GlassCard hover className="group">
        <div className="text-4xl mb-4">🌟</div>
        <h2 className="text-2xl font-bold text-white mb-4">{t('about.vision.title')}</h2>
        <p className="text-white/80 leading-relaxed">{t('about.vision.description')}</p>
      </GlassCard>
    </section>
  );
};

export default MissionVision;
