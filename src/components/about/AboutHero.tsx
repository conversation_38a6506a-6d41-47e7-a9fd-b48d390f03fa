
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

const AboutHero = () => {
  const { t } = useLanguage();

  return (
    <section className="text-center">
      <GlassCard>
        <h1 className="text-5xl md:text-6xl font-extrabold text-white mb-6">
          {t('about.hero.title')}
        </h1>
        <p className="text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
          {t('about.hero.subtitle')}
        </p>
      </GlassCard>
    </section>
  );
};

export default AboutHero;
