
import React from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

const doctorsData = [
  { 
    id: 'dr_smith',
    initials: 'DS',
    gradient: 'from-pink-400 to-purple-600'
  },
  { 
    id: 'dr_lee',
    initials: 'DL',
    gradient: 'from-blue-400 to-indigo-600'
  },
  { 
    id: 'dr_chen',
    initials: 'DC',
    gradient: 'from-green-400 to-teal-600'
  },
];

const DoctorsSection = () => {
  const { t } = useLanguage();

  return (
    <section className="space-y-8">
      <h3 className="text-3xl font-semibold text-center text-white">{t('about.doctors.title')}</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
        {doctorsData.map((doctor) => (
          <GlassCard key={doctor.id} hover className="text-center group">
            <Avatar className={`w-24 h-24 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
              <AvatarFallback className={`bg-gradient-to-br ${doctor.gradient} text-white text-2xl font-bold`}>
                {doctor.initials}
              </AvatarFallback>
            </Avatar>
            <h4 className="text-xl font-semibold text-white mb-2">
              {t(`about.doctors.team.${doctor.id}.name`)}
            </h4>
            <p className="text-white/70">
              {t(`about.doctors.team.${doctor.id}.role`)}
            </p>
          </GlassCard>
        ))}
      </div>
    </section>
  );
};

export default DoctorsSection;
