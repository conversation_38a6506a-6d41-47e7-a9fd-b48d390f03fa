/**
 * Lullaby Clinic - Integration Test Panel
 * Development component for testing backend integrations
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import React, { useState } from 'react';
import { useHybridDoctors, useHybridServices, useHybridBlogPosts } from '@/hooks/useHybridContent';
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';
import { useDataSourceDebug } from '@/hooks/useHybridContent';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  Server, 
  Users, 
  FileText, 
  Settings,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react';
import BackendStatus from './BackendStatus';
import MediaUpload from './MediaUpload';

interface IntegrationTestPanelProps {
  className?: string;
}

export const IntegrationTestPanel: React.FC<IntegrationTestPanelProps> = ({ 
  className = '' 
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  // Hooks for testing
  const auth = useEnhancedAuth();
  const doctorsQuery = useHybridDoctors({ limit: 5 });
  const servicesQuery = useHybridServices({ limit: 5 });
  const blogPostsQuery = useHybridBlogPosts({ limit: 3 });
  const debugInfo = useDataSourceDebug();

  // Test functions
  const runConnectivityTest = async () => {
    setTestResults(prev => ({ ...prev, connectivity: 'running' }));
    
    try {
      const results = await Promise.allSettled([
        doctorsQuery.refetch(),
        servicesQuery.refetch(),
        blogPostsQuery.refetch(),
      ]);

      const connectivityResults = {
        doctors: results[0].status === 'fulfilled' ? 'success' : 'failed',
        services: results[1].status === 'fulfilled' ? 'success' : 'failed',
        blogPosts: results[2].status === 'fulfilled' ? 'success' : 'failed',
        timestamp: new Date().toISOString(),
      };

      setTestResults(prev => ({ 
        ...prev, 
        connectivity: connectivityResults 
      }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        connectivity: { error: error instanceof Error ? error.message : 'Unknown error' }
      }));
    }
  };

  const runAuthTest = async () => {
    setTestResults(prev => ({ ...prev, auth: 'running' }));
    
    try {
      await auth.refreshSession();
      
      const authResults = {
        isAuthenticated: auth.isAuthenticated,
        role: auth.role,
        permissions: auth.permissions?.length || 0,
        payloadUser: !!auth.payloadUser,
        timestamp: new Date().toISOString(),
      };

      setTestResults(prev => ({ 
        ...prev, 
        auth: authResults 
      }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        auth: { error: error instanceof Error ? error.message : 'Unknown error' }
      }));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getDataSourceBadge = (source: string) => {
    const variants: Record<string, any> = {
      payload: { variant: 'default', color: 'bg-blue-500' },
      supabase: { variant: 'secondary', color: 'bg-green-500' },
      error: { variant: 'destructive', color: 'bg-red-500' },
      unknown: { variant: 'outline', color: 'bg-gray-500' },
    };

    const config = variants[source] || variants.unknown;
    
    return (
      <Badge variant={config.variant} className="text-xs">
        {source.charAt(0).toUpperCase() + source.slice(1)}
      </Badge>
    );
  };

  if (!import.meta.env.DEV) {
    return null; // Only show in development
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Backend Integration Test Panel
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showDetails ? 'Hide' : 'Show'} Details
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="status" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="status">Status</TabsTrigger>
              <TabsTrigger value="data">Data</TabsTrigger>
              <TabsTrigger value="auth">Auth</TabsTrigger>
              <TabsTrigger value="media">Media</TabsTrigger>
              <TabsTrigger value="tests">Tests</TabsTrigger>
            </TabsList>

            {/* System Status */}
            <TabsContent value="status" className="space-y-4">
              <BackendStatus showDetails={true} />
            </TabsContent>

            {/* Data Sources */}
            <TabsContent value="data" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Doctors
                      </div>
                      {getDataSourceBadge(debugInfo.dataSources.doctors)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div>Count: {doctorsQuery.data?.doctors?.length || 0}</div>
                      <div>Loading: {debugInfo.isLoading.doctors ? 'Yes' : 'No'}</div>
                      {debugInfo.errors.doctors && (
                        <div className="text-red-500 text-xs">
                          Error: {debugInfo.errors.doctors}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4" />
                        Services
                      </div>
                      {getDataSourceBadge(debugInfo.dataSources.services)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div>Count: {servicesQuery.data?.services?.length || 0}</div>
                      <div>Loading: {debugInfo.isLoading.services ? 'Yes' : 'No'}</div>
                      {debugInfo.errors.services && (
                        <div className="text-red-500 text-xs">
                          Error: {debugInfo.errors.services}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Blog Posts
                      </div>
                      {getDataSourceBadge(debugInfo.dataSources.blogPosts)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div>Count: {blogPostsQuery.data?.posts?.length || 0}</div>
                      <div>Loading: {debugInfo.isLoading.blogPosts ? 'Yes' : 'No'}</div>
                      {debugInfo.errors.blogPosts && (
                        <div className="text-red-500 text-xs">
                          Error: {debugInfo.errors.blogPosts}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Authentication */}
            <TabsContent value="auth" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Authentication Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="font-medium">Authenticated:</div>
                      <div>{auth.isAuthenticated ? 'Yes' : 'No'}</div>
                    </div>
                    <div>
                      <div className="font-medium">Role:</div>
                      <div>{auth.role || 'None'}</div>
                    </div>
                    <div>
                      <div className="font-medium">Permissions:</div>
                      <div>{auth.permissions?.length || 0}</div>
                    </div>
                    <div>
                      <div className="font-medium">Payload User:</div>
                      <div>{auth.payloadUser ? 'Synced' : 'Not synced'}</div>
                    </div>
                  </div>
                  
                  {showDetails && auth.permissions && auth.permissions.length > 0 && (
                    <div className="mt-4">
                      <div className="font-medium text-sm mb-2">Permissions:</div>
                      <div className="flex flex-wrap gap-1">
                        {auth.permissions.map(permission => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Media Upload */}
            <TabsContent value="media" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Media Upload Test</CardTitle>
                </CardHeader>
                <CardContent>
                  <MediaUpload
                    onUploadComplete={(media) => {
                      console.log('Upload completed:', media);
                      setTestResults(prev => ({
                        ...prev,
                        lastUpload: { success: true, media, timestamp: new Date().toISOString() }
                      }));
                    }}
                    onUploadError={(error) => {
                      console.error('Upload failed:', error);
                      setTestResults(prev => ({
                        ...prev,
                        lastUpload: { success: false, error, timestamp: new Date().toISOString() }
                      }));
                    }}
                    acceptedFileTypes={['image/*']}
                    maxFileSize={5 * 1024 * 1024} // 5MB
                  />
                  
                  {testResults.lastUpload && (
                    <Alert className="mt-4">
                      <AlertDescription>
                        Last upload: {testResults.lastUpload.success ? 'Success' : 'Failed'}
                        {testResults.lastUpload.error && ` - ${testResults.lastUpload.error}`}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tests */}
            <TabsContent value="tests" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Connectivity Test</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      onClick={runConnectivityTest}
                      disabled={testResults.connectivity === 'running'}
                      className="w-full mb-4"
                    >
                      {testResults.connectivity === 'running' ? (
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Database className="h-4 w-4 mr-2" />
                      )}
                      Test Data Sources
                    </Button>
                    
                    {testResults.connectivity && typeof testResults.connectivity === 'object' && (
                      <div className="space-y-2 text-sm">
                        {Object.entries(testResults.connectivity).map(([key, value]) => (
                          key !== 'timestamp' && (
                            <div key={key} className="flex items-center justify-between">
                              <span className="capitalize">{key}:</span>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(value as string)}
                                <span className="capitalize">{value as string}</span>
                              </div>
                            </div>
                          )
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Authentication Test</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      onClick={runAuthTest}
                      disabled={testResults.auth === 'running'}
                      className="w-full mb-4"
                    >
                      {testResults.auth === 'running' ? (
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Users className="h-4 w-4 mr-2" />
                      )}
                      Test Authentication
                    </Button>
                    
                    {testResults.auth && typeof testResults.auth === 'object' && (
                      <div className="space-y-2 text-sm">
                        {Object.entries(testResults.auth).map(([key, value]) => (
                          key !== 'timestamp' && (
                            <div key={key} className="flex items-center justify-between">
                              <span className="capitalize">{key}:</span>
                              <span>{String(value)}</span>
                            </div>
                          )
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default IntegrationTestPanel;
