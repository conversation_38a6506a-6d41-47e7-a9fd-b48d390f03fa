/**
 * Lullaby Clinic - Media Upload Component
 * Handles file uploads to Payload CMS media system
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import React, { useCallback, useState, useRef } from 'react';
import { useUploadPayloadMedia } from '@/hooks/usePayloadCMS';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  X, 
  FileImage, 
  FileVideo, 
  File,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface MediaUploadProps {
  onUploadComplete?: (media: any) => void;
  onUploadError?: (error: string) => void;
  acceptedFileTypes?: string[];
  maxFileSize?: number; // in bytes
  multiple?: boolean;
  className?: string;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
  result?: any;
}

export const MediaUpload: React.FC<MediaUploadProps> = ({
  onUploadComplete,
  onUploadError,
  acceptedFileTypes = ['image/*', 'video/*', 'application/pdf'],
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  multiple = false,
  className = ''
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadMutation = useUploadPayloadMedia();

  const handleFiles = useCallback(async (files: FileList | null) => {
    if (!files) return;

    const acceptedFiles = Array.from(files).filter(file => {
      // Check file size
      if (file.size > maxFileSize) {
        onUploadError?.(`File ${file.name} is too large. Maximum size is ${formatFileSize(maxFileSize)}`);
        return false;
      }

      // Check file type
      const isAccepted = acceptedFileTypes.some(type => {
        if (type.endsWith('/*')) {
          return file.type.startsWith(type.slice(0, -1));
        }
        return file.type === type;
      });

      if (!isAccepted) {
        onUploadError?.(`File ${file.name} type is not supported`);
        return false;
      }

      return true;
    });

    if (acceptedFiles.length === 0) return;
    const newUploads: UploadingFile[] = acceptedFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const,
    }));

    setUploadingFiles(prev => [...prev, ...newUploads]);

    // Upload files one by one
    for (let i = 0; i < acceptedFiles.length; i++) {
      const file = acceptedFiles[i];
      const uploadIndex = uploadingFiles.length + i;

      try {
        // Update progress to show upload starting
        setUploadingFiles(prev => 
          prev.map((upload, index) => 
            index === uploadIndex 
              ? { ...upload, progress: 10 }
              : upload
          )
        );

        // Simulate progress updates (since we don't have real progress from the API)
        const progressInterval = setInterval(() => {
          setUploadingFiles(prev => 
            prev.map((upload, index) => 
              index === uploadIndex && upload.progress < 90
                ? { ...upload, progress: upload.progress + 10 }
                : upload
            )
          );
        }, 200);

        const result = await uploadMutation.mutateAsync({ 
          file, 
          alt: file.name.split('.')[0] 
        });

        clearInterval(progressInterval);

        if (result.success) {
          setUploadingFiles(prev => 
            prev.map((upload, index) => 
              index === uploadIndex 
                ? { 
                    ...upload, 
                    progress: 100, 
                    status: 'success' as const,
                    result: result.data 
                  }
                : upload
            )
          );

          onUploadComplete?.(result.data);
        } else {
          throw new Error(result.error || 'Upload failed');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';
        
        setUploadingFiles(prev => 
          prev.map((upload, index) => 
            index === uploadIndex 
              ? { 
                  ...upload, 
                  status: 'error' as const,
                  error: errorMessage 
                }
              : upload
          )
        );

        onUploadError?.(errorMessage);
      }
    }
  }, [uploadMutation, onUploadComplete, onUploadError, uploadingFiles.length]);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    handleFiles(e.dataTransfer.files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const removeFile = (index: number) => {
    setUploadingFiles(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <FileImage className="h-6 w-6 text-blue-500" />;
    } else if (file.type.startsWith('video/')) {
      return <FileVideo className="h-6 w-6 text-purple-500" />;
    } else {
      return <File className="h-6 w-6 text-gray-500" />;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={openFileDialog}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
              }
            `}
          >
            <input
              ref={fileInputRef}
              type="file"
              multiple={multiple}
              accept={acceptedFileTypes.join(',')}
              onChange={handleFileInputChange}
              className="hidden"
            />
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />

            {isDragActive ? (
              <p className="text-blue-600 font-medium">Drop files here...</p>
            ) : (
              <div>
                <p className="text-gray-600 font-medium mb-2">
                  Drag & drop files here, or click to select
                </p>
                <p className="text-sm text-gray-500">
                  Supports: {acceptedFileTypes.join(', ')}
                  (Max: {formatFileSize(maxFileSize)})
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <div className="space-y-3">
          {uploadingFiles.map((upload, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  {getFileIcon(upload.file)}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium truncate">
                        {upload.file.name}
                      </p>
                      <div className="flex items-center gap-2">
                        {upload.status === 'uploading' && (
                          <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                        )}
                        {upload.status === 'success' && (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                        {upload.status === 'error' && (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>{formatFileSize(upload.file.size)}</span>
                      {upload.status === 'uploading' && (
                        <>
                          <span>•</span>
                          <span>{upload.progress}%</span>
                        </>
                      )}
                    </div>

                    {upload.status === 'uploading' && (
                      <Progress value={upload.progress} className="mt-2 h-1" />
                    )}

                    {upload.status === 'error' && upload.error && (
                      <Alert className="mt-2">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription className="text-xs">
                          {upload.error}
                        </AlertDescription>
                      </Alert>
                    )}

                    {upload.status === 'success' && upload.result && (
                      <div className="mt-2 text-xs text-green-600">
                        Upload successful • ID: {upload.result.id}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default MediaUpload;
