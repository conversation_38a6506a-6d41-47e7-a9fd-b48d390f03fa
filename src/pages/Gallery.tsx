
import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from '@/components/gallery/GlassCard';
import GalleryItem from '@/components/gallery/GalleryItem';
import GalleryStats from '@/components/gallery/GalleryStats';
import GalleryBackground from '@/components/gallery/GalleryBackground';

// Gallery data
const galleryData = [
  {
    id: 'facial_results',
    category: 'facial',
    beforeImage: '✨',
    afterImage: '🌟',
    featured: true,
    video: false
  },
  {
    id: 'botox_results',
    category: 'botox',
    beforeImage: '👤',
    afterImage: '😊',
    featured: true,
    video: false
  },
  {
    id: 'laser_results',
    category: 'laser',
    beforeImage: '🔥',
    afterImage: '✨',
    featured: false,
    video: true
  },
  {
    id: 'filler_results',
    category: 'filler',
    beforeImage: '💎',
    afterImage: '💫',
    featured: false,
    video: false
  }
];

const Gallery = () => {
  const { t, currentLanguage } = useLanguage();
  const [activeTab, setActiveTab] = useState('all');

  const filteredItems = galleryData.filter(item => 
    activeTab === 'all' || item.category === activeTab
  );

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('gallery.hero.title')} | Lullaby Clinic`}
        description={t('gallery.hero.subtitle')}
        keywords="lullaby clinic gallery, before after results, facial results, botox results, laser treatment"
      />
      
      <div
        className="min-h-screen relative"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        {/* Animated background elements */}
        <GalleryBackground />

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-6 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <GlassCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-white mb-6">
                {t('gallery.hero.title')}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                {t('gallery.hero.subtitle')}
              </p>
            </GlassCard>
          </section>

          {/* Filter Tabs */}
          <section className="text-center">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full max-w-lg mx-auto grid-cols-5 bg-white/10 backdrop-blur-md">
                <TabsTrigger value="all" className="data-[state=active]:bg-white/20 text-white">
                  All
                </TabsTrigger>
                <TabsTrigger value="facial" className="data-[state=active]:bg-white/20 text-white">
                  Facial
                </TabsTrigger>
                <TabsTrigger value="botox" className="data-[state=active]:bg-white/20 text-white">
                  Botox
                </TabsTrigger>
                <TabsTrigger value="laser" className="data-[state=active]:bg-white/20 text-white">
                  Laser
                </TabsTrigger>
                <TabsTrigger value="filler" className="data-[state=active]:bg-white/20 text-white">
                  Filler
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </section>

          {/* Gallery Grid */}
          <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredItems.map((item) => (
              <GalleryItem key={item.id} item={item} />
            ))}
          </section>

          {/* Stats Section */}
          <GalleryStats />
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Gallery;
