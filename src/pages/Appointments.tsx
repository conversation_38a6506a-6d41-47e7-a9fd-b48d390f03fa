
import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Clock, User, MapPin, Phone, Mail } from 'lucide-react';
import { format } from 'date-fns';
import { useLanguage } from '@/contexts/LanguageContext';

// Available time slots
const timeSlots = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00'
];

// Services data
const services = [
  { id: 'facial_hydra', duration: 60, price: 2500 },
  { id: 'facial_diamond', duration: 90, price: 3500 },
  { id: 'botox_forehead', duration: 30, price: 8000 },
  { id: 'filler_lips', duration: 45, price: 15000 },
  { id: 'laser_hair', duration: 30, price: 3000 }
];

// Doctors data
const doctors = [
  { id: 'dr_smith', specialty: 'facial' },
  { id: 'dr_lee', specialty: 'botox' },
  { id: 'dr_chen', specialty: 'laser' }
];

// Glass effect card component
const GlassCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      backdrop-blur-md bg-white/10 border border-white/20 shadow-xl
      ${hover ? 'hover:bg-white/15 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
      backdropFilter: 'blur(16px)',
      WebkitBackdropFilter: 'blur(16px)',
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

GlassCard.displayName = 'GlassCard';

const Appointments = () => {
  const { t, currentLanguage } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [selectedDoctor, setSelectedDoctor] = useState('');
  const [selectedBranch, setSelectedBranch] = useState('');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    notes: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Appointment booking:', {
      date: selectedDate,
      time: selectedTime,
      service: selectedService,
      doctor: selectedDoctor,
      branch: selectedBranch,
      ...formData
    });
  };

  const selectedServiceData = services.find(s => s.id === selectedService);

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('appointments.hero.title')} | Lullaby Clinic`}
        description={t('appointments.hero.subtitle')}
        keywords="book appointment lullaby clinic, medical appointment, beauty consultation"
      />
      
      <div
        className="min-h-screen relative"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-pink-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div
            className="absolute top-40 right-20 w-96 h-96 bg-purple-300/15 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '4s' }}
          ></div>
        </div>

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-6 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <GlassCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-white mb-6">
                {t('appointments.hero.title')}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                {t('appointments.hero.subtitle')}
              </p>
            </GlassCard>
          </section>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Service Selection */}
            <section>
              <GlassCard>
                <h2 className="text-2xl font-bold text-white mb-6">
                  {t('appointments.selectService')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Select value={selectedService} onValueChange={setSelectedService}>
                    <SelectTrigger className="bg-white/10 border-white/20 text-white">
                      <User className="h-4 w-4 mr-2" />
                      <SelectValue placeholder={t('appointments.chooseService')} />
                    </SelectTrigger>
                    <SelectContent>
                      {services.map((service) => (
                        <SelectItem key={service.id} value={service.id}>
                          {t(`appointments.services.${service.id}`)} - ฿{service.price}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={selectedDoctor} onValueChange={setSelectedDoctor}>
                    <SelectTrigger className="bg-white/10 border-white/20 text-white">
                      <User className="h-4 w-4 mr-2" />
                      <SelectValue placeholder={t('appointments.chooseDoctor')} />
                    </SelectTrigger>
                    <SelectContent>
                      {doctors.map((doctor) => (
                        <SelectItem key={doctor.id} value={doctor.id}>
                          {t(`appointments.doctors.${doctor.id}`)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={selectedBranch} onValueChange={setSelectedBranch}>
                    <SelectTrigger className="bg-white/10 border-white/20 text-white">
                      <MapPin className="h-4 w-4 mr-2" />
                      <SelectValue placeholder={t('appointments.chooseBranch')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="central">Central Bangkok</SelectItem>
                      <SelectItem value="sukhumvit">Sukhumvit</SelectItem>
                      <SelectItem value="silom">Silom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {selectedServiceData && (
                  <div className="mt-4 p-4 bg-white/5 rounded-lg">
                    <div className="flex justify-between items-center text-white">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>{selectedServiceData.duration} minutes</span>
                      </div>
                      <div className="text-lg font-semibold">
                        ฿{selectedServiceData.price}
                      </div>
                    </div>
                  </div>
                )}
              </GlassCard>
            </section>

            {/* Date & Time Selection */}
            <section>
              <GlassCard>
                <h2 className="text-2xl font-bold text-white mb-6">
                  {t('appointments.selectDateTime')}
                </h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Date Picker */}
                  <div>
                    <label className="block text-white mb-4">
                      {t('appointments.selectDate')}
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal bg-white/10 border-white/20 text-white hover:bg-white/20"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {selectedDate ? (
                            format(selectedDate, 'PPP')
                          ) : (
                            <span>{t('appointments.pickDate')}</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={selectedDate}
                          onSelect={setSelectedDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Time Slots */}
                  <div>
                    <label className="block text-white mb-4">
                      {t('appointments.selectTime')}
                    </label>
                    <div className="grid grid-cols-3 gap-2 max-h-48 overflow-y-auto">
                      {timeSlots.map((time) => (
                        <Button
                          key={time}
                          type="button"
                          variant={selectedTime === time ? "default" : "outline"}
                          className={`text-sm ${
                            selectedTime === time
                              ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                              : 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                          }`}
                          onClick={() => setSelectedTime(time)}
                        >
                          {time}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </GlassCard>
            </section>

            {/* Personal Information */}
            <section>
              <GlassCard>
                <h2 className="text-2xl font-bold text-white mb-6">
                  {t('appointments.personalInfo')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-white mb-2">
                      {t('appointments.firstName')}
                    </label>
                    <Input
                      placeholder={t('appointments.firstNamePlaceholder')}
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    />
                  </div>
                  <div>
                    <label className="block text-white mb-2">
                      {t('appointments.lastName')}
                    </label>
                    <Input
                      placeholder={t('appointments.lastNamePlaceholder')}
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    />
                  </div>
                  <div>
                    <label className="block text-white mb-2">
                      {t('appointments.phone')}
                    </label>
                    <Input
                      type="tel"
                      placeholder={t('appointments.phonePlaceholder')}
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    />
                  </div>
                  <div>
                    <label className="block text-white mb-2">
                      {t('appointments.email')}
                    </label>
                    <Input
                      type="email"
                      placeholder={t('appointments.emailPlaceholder')}
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-white mb-2">
                      {t('appointments.notes')}
                    </label>
                    <Textarea
                      placeholder={t('appointments.notesPlaceholder')}
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                      rows={4}
                    />
                  </div>
                </div>
              </GlassCard>
            </section>

            {/* Submit Button */}
            <section className="text-center">
              <GlassCard>
                <Button 
                  type="submit"
                  size="lg"
                  className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0 px-12 py-4 text-lg"
                  disabled={!selectedDate || !selectedTime || !selectedService || !formData.firstName || !formData.phone}
                >
                  {t('appointments.confirmBooking')}
                </Button>
                <p className="text-white/70 mt-4 text-sm">
                  {t('appointments.confirmationNote')}
                </p>
              </GlassCard>
            </section>
          </form>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Appointments;
