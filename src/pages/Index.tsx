import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import HeroSection from '@/components/HeroSection';
import FlashSalesBanner from '@/components/FlashSalesBanner';
import ServicesSection from '@/components/ServicesSection';
import BeforeAfterShowcase from '@/components/BeforeAfterShowcase';
import MonthlyPromotions from '@/components/MonthlyPromotions';
import TestimonialsSection from '@/components/TestimonialsSection';
import BeforeAfterGallery from '@/components/BeforeAfterGallery';
import BlogSection from '@/components/BlogSection';
import NewsletterSubscription from '@/components/NewsletterSubscription';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { usePerformance } from '@/hooks/usePerformance';
import { translations } from '@/utils/translations';
import IntegrationTestPanel from '@/components/IntegrationTestPanel';

const Index = () => {
  const { lang } = useParams<{ lang: string }>();
  const location = useLocation();
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    // Language detection priority: URL param > localStorage > browser > default
    if (lang && ['en', 'th', 'zh'].includes(lang)) {
      return lang;
    }
    
    const savedLang = localStorage.getItem('preferred-language');
    if (savedLang && ['en', 'th', 'zh'].includes(savedLang)) {
      return savedLang;
    }
    
    const browserLang = navigator.language.split('-')[0];
    if (['en', 'th', 'zh'].includes(browserLang)) {
      return browserLang;
    }
    
    return 'en';
  });

  // Performance monitoring
  const { metrics, score, isLoading: isPerformanceLoading } = usePerformance();

  const handleLanguageChange = (newLang: string) => {
    setCurrentLanguage(newLang);
    localStorage.setItem('preferred-language', newLang);
    
    // Update URL without page reload
    const newPath = newLang === 'en' ? '/' : `/${newLang}`;
    window.history.pushState({}, '', newPath);
    
    console.log(`Language changed to: ${newLang}`);
  };

  const currentTranslations = translations[currentLanguage as keyof typeof translations];

  // Update document language and direction
  useEffect(() => {
    document.documentElement.lang = currentLanguage;
    document.documentElement.dir = currentLanguage === 'th' ? 'ltr' : 'ltr'; // All languages are LTR for this site
    
    // Update page title in browser tab
    const titles = {
      en: 'Lullaby Clinic - Premium Beauty & Medical Aesthetics',
      th: 'คลินิกลูลลาบาย - คลินิกความงามและการแพทย์เสริมความงามระดับพรีเมียม',
      zh: '摇篮诊所 - 高端美容医疗美学'
    };
    document.title = titles[currentLanguage as keyof typeof titles];
  }, [currentLanguage]);

  // Performance monitoring effect
  useEffect(() => {
    if (!isPerformanceLoading && score && import.meta.env.DEV) {
      console.log('Performance Score:', score);
      console.log('Performance Metrics:', metrics);
    }
  }, [score, metrics, isPerformanceLoading]);

  // Handle language from URL parameter
  useEffect(() => {
    if (lang && ['en', 'th', 'zh'].includes(lang) && lang !== currentLanguage) {
      setCurrentLanguage(lang);
      localStorage.setItem('preferred-language', lang);
    }
  }, [lang, currentLanguage]);

  // Canonical URL for SEO
  const canonicalUrl = `https://lullabyclinic.com${location.pathname}`;

  return (
    <div className="min-h-screen bg-background">
      {/* SEO Head with multilingual support */}
      <SEOHead
        lang={currentLanguage}
        canonicalUrl={canonicalUrl}
        ogImage="/logo.svg"
      />
      
      <Navigation 
        currentLang={currentLanguage}
        onLanguageChange={handleLanguageChange}
        translations={currentTranslations}
      />
      
      {/* Flash Sales Banner - High visibility */}
      <ErrorBoundary level="component">
        <FlashSalesBanner translations={currentTranslations} language={currentLanguage} />
      </ErrorBoundary>

      <main>
        <ErrorBoundary level="component">
          <HeroSection translations={currentTranslations} />
        </ErrorBoundary>
        
        <ErrorBoundary level="component">
          <ServicesSection translations={currentTranslations} />
        </ErrorBoundary>

        {/* Before/After Showcase - Interactive comparison */}
        <ErrorBoundary level="component">
          <BeforeAfterShowcase translations={currentTranslations} />
        </ErrorBoundary>

        {/* Monthly Promotions - Create urgency */}
        <ErrorBoundary level="component">
          <MonthlyPromotions translations={currentTranslations} />
        </ErrorBoundary>
        
        <ErrorBoundary level="component">
          <TestimonialsSection translations={currentTranslations} />
        </ErrorBoundary>
        
        <ErrorBoundary level="component">
          <BeforeAfterGallery translations={currentTranslations} />
        </ErrorBoundary>
        
        <ErrorBoundary level="component">
          <BlogSection translations={currentTranslations} />
        </ErrorBoundary>

        {/* Newsletter Subscription - Lead capture */}
        <ErrorBoundary level="component">
          <NewsletterSubscription 
            translations={currentTranslations}
            variant="default"
            showIncentives={true}
            showSocialProof={true}
          />
        </ErrorBoundary>
      </main>
      
      <Footer translations={currentTranslations} />
      
      {/* Performance monitoring in development */}
      {import.meta.env.DEV && score && (
        <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs max-w-xs">
          <div>Performance: {score.overall}</div>
          {metrics.lcp && <div>LCP: {Math.round(metrics.lcp)}ms</div>}
          {metrics.fcp && <div>FCP: {Math.round(metrics.fcp)}ms</div>}
        </div>
      )}

      {/* Backend Integration Test Panel - Development Only */}
      {import.meta.env.DEV && (
        <div className="fixed bottom-4 left-4 max-w-sm">
          <IntegrationTestPanel />
        </div>
      )}
    </div>
  );
};

export default Index;
