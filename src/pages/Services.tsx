import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Clock, DollarSign, CheckCircle, ArrowRight } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Treatment data
const treatmentsData = [
  {
    id: 'facial',
    icon: '✨',
    category: 'skincare'
  },
  {
    id: 'botox',
    icon: '💉',
    category: 'anti-aging'
  },
  {
    id: 'laser',
    icon: '⚡',
    category: 'technology'
  },
  {
    id: 'filler',
    icon: '💎',
    category: 'enhancement'
  }
];

// Glass effect card component
const GlassCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      backdrop-blur-md bg-white/10 border border-white/20 shadow-xl
      ${hover ? 'hover:bg-white/15 hover:scale-105 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
      backdropFilter: 'blur(16px)',
      WebkitBackdropFilter: 'blur(16px)',
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

GlassCard.displayName = 'GlassCard';

// Treatment Card Component
const TreatmentCard = memo(({ treatment }: { treatment: typeof treatmentsData[0] }) => {
  const { t } = useLanguage();
  
  return (
    <GlassCard hover className="h-full">
      <div className="text-center space-y-6">
        <div className="text-6xl">{treatment.icon}</div>
        <div>
          <h3 className="text-2xl font-bold text-white mb-3">
            {t(`servicesPage.treatments.${treatment.id}.title`)}
          </h3>
          <p className="text-white/80 leading-relaxed mb-4">
            {t(`servicesPage.treatments.${treatment.id}.description`)}
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between text-white/70">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>{t(`servicesPage.treatments.${treatment.id}.duration`)}</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              <span className="font-semibold text-white">
                {t(`servicesPage.treatments.${treatment.id}.price`)}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            {['Smooth, glowing skin', 'Hydration boost', 'Pore refinement', 'Anti-aging benefits'].map((benefit: string, idx: number) => (
              <div key={idx} className="flex items-center gap-2 text-white/80">
                <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                <span className="text-sm">{benefit}</span>
              </div>
            ))}
          </div>
          
          <Button 
            className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0"
          >
            {t('common.bookNow')} <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </GlassCard>
  );
});

TreatmentCard.displayName = 'TreatmentCard';

const Services = () => {
  const { t, currentLanguage } = useLanguage();
  const [activeTab, setActiveTab] = useState('all');

  const filteredTreatments = treatmentsData.filter(treatment => 
    activeTab === 'all' || treatment.category === activeTab
  );

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('servicesPage.hero.title')} | Lullaby Clinic`}
        description={t('servicesPage.hero.subtitle')}
        keywords="lullaby clinic services, facial treatment, botox, laser, dermal fillers, beauty treatments"
      />
      
      <div
        className="min-h-screen relative"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-pink-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div
            className="absolute top-40 right-20 w-96 h-96 bg-purple-300/15 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '4s' }}
          ></div>
          <div
            className="absolute top-1/2 right-10 w-64 h-64 bg-yellow-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '6s' }}
          ></div>
        </div>

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-6 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <GlassCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-white mb-6">
                {t('servicesPage.hero.title')}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                {t('servicesPage.hero.subtitle')}
              </p>
            </GlassCard>
          </section>

          {/* Treatments Section */}
          <section className="space-y-8">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-white mb-6">
                {t('servicesPage.treatments.title')}
              </h2>
              
              {/* Category Tabs */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-12">
                <TabsList className="grid w-full max-w-md mx-auto grid-cols-5 bg-white/10 backdrop-blur-md">
                  <TabsTrigger value="all" className="data-[state=active]:bg-white/20 text-white">
                    All
                  </TabsTrigger>
                  <TabsTrigger value="skincare" className="data-[state=active]:bg-white/20 text-white">
                    Skincare
                  </TabsTrigger>
                  <TabsTrigger value="anti-aging" className="data-[state=active]:bg-white/20 text-white">
                    Anti-Aging
                  </TabsTrigger>
                  <TabsTrigger value="technology" className="data-[state=active]:bg-white/20 text-white">
                    Technology
                  </TabsTrigger>
                  <TabsTrigger value="enhancement" className="data-[state=active]:bg-white/20 text-white">
                    Enhancement
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Treatments Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
              {filteredTreatments.map((treatment) => (
                <TreatmentCard key={treatment.id} treatment={treatment} />
              ))}
            </div>
          </section>

          {/* Booking CTA Section */}
          <section className="text-center">
            <GlassCard>
              <div className="max-w-2xl mx-auto space-y-6">
                <h2 className="text-3xl font-bold text-white">
                  {t('servicesPage.booking.title')}
                </h2>
                <p className="text-xl text-white/80">
                  {t('servicesPage.booking.subtitle')}
                </p>
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0 px-8 py-4 text-lg"
                >
                  {t('servicesPage.booking.button')} <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </GlassCard>
          </section>

          {/* Why Choose Us Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-white text-center">
              Why Choose Lullaby Clinic?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">👨‍⚕️</div>
                  <h3 className="text-xl font-bold text-white">Expert Doctors</h3>
                  <p className="text-white/80">
                    Our team of certified medical professionals with years of experience in aesthetic medicine
                  </p>
                </div>
              </GlassCard>
              
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🔬</div>
                  <h3 className="text-xl font-bold text-white">Advanced Technology</h3>
                  <p className="text-white/80">
                    State-of-the-art equipment and latest techniques for safe and effective treatments
                  </p>
                </div>
              </GlassCard>
              
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">💝</div>
                  <h3 className="text-xl font-bold text-white">Personalized Care</h3>
                  <p className="text-white/80">
                    Customized treatment plans tailored to your unique beauty goals and needs
                  </p>
                </div>
              </GlassCard>
            </div>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Services;
