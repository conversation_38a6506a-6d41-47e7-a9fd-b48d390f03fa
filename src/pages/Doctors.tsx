import React, { memo } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Award, GraduationCap, Stethoscope, ArrowRight } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Doctor data
const doctorsData = [
  {
    id: 'dr_smith',
    initials: 'DS',
    gradient: 'from-pink-400 to-purple-600'
  },
  {
    id: 'dr_lee',
    initials: 'DL',
    gradient: 'from-blue-400 to-indigo-600'
  },
  {
    id: 'dr_chen',
    initials: 'DC',
    gradient: 'from-green-400 to-teal-600'
  }
];

// Glass effect card component
const GlassCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      backdrop-blur-md bg-white/10 border border-white/20 shadow-xl
      ${hover ? 'hover:bg-white/15 hover:scale-105 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
      backdropFilter: 'blur(16px)',
      WebkitBackdropFilter: 'blur(16px)',
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

GlassCard.displayName = 'GlassCard';

// Doctor Card Component
const DoctorCard = memo(({ doctor }: { doctor: typeof doctorsData[0] }) => {
  const { t } = useLanguage();
  
  return (
    <GlassCard hover className="h-full">
      <div className="text-center space-y-6">
        <Avatar className={`w-32 h-32 mx-auto bg-gradient-to-br ${doctor.gradient} text-white`}>
          <AvatarFallback className="text-3xl font-bold">
            {doctor.initials}
          </AvatarFallback>
        </Avatar>
        
        <div>
          <h3 className="text-2xl font-bold text-white mb-2">
            {t(`doctorsPage.team.${doctor.id}.name`)}
          </h3>
          <p className="text-white/80 text-lg font-medium mb-4">
            {t(`doctorsPage.team.${doctor.id}.role`)}
          </p>
        </div>

        <div className="space-y-4 text-left">
          {/* Experience */}
          <div className="flex items-start gap-3">
            <Calendar className="w-5 h-5 text-pink-300 mt-1 flex-shrink-0" />
            <div>
              <p className="text-white/70 text-sm">Experience</p>
              <p className="text-white font-medium">
                {t(`doctorsPage.team.${doctor.id}.experience`)}
              </p>
            </div>
          </div>

          {/* Specialties */}
          <div className="flex items-start gap-3">
            <Stethoscope className="w-5 h-5 text-green-300 mt-1 flex-shrink-0" />
            <div>
              <p className="text-white/70 text-sm mb-2">Specialties</p>
              <div className="flex flex-wrap gap-2">
                {['Dermatology', 'Aesthetic Medicine', 'Anti-Aging'].map((specialty: string, idx: number) => (
                  <Badge key={idx} variant="secondary" className="bg-white/20 text-white text-xs">
                    {specialty}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Education */}
          <div className="flex items-start gap-3">
            <GraduationCap className="w-5 h-5 text-blue-300 mt-1 flex-shrink-0" />
            <div>
              <p className="text-white/70 text-sm mb-2">Education</p>
              <ul className="space-y-1">
                {['MD, Harvard Medical School', 'Dermatology Residency, Johns Hopkins', 'Fellowship in Aesthetic Medicine'].map((edu: string, idx: number) => (
                  <li key={idx} className="text-white text-sm">
                    • {edu}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="pt-4">
          <p className="text-white/80 text-sm leading-relaxed mb-4">
            {t(`doctorsPage.team.${doctor.id}.description`)}
          </p>
          
          <Button 
            className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0"
          >
            Book Consultation <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </GlassCard>
  );
});

DoctorCard.displayName = 'DoctorCard';

const Doctors = () => {
  const { t, currentLanguage } = useLanguage();

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('doctorsPage.hero.title')} | Lullaby Clinic`}
        description={t('doctorsPage.hero.subtitle')}
        keywords="lullaby clinic doctors, aesthetic medicine specialists, dermatology experts, beauty doctors"
      />
      
      <div
        className="min-h-screen relative"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-pink-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div
            className="absolute top-40 right-20 w-96 h-96 bg-purple-300/15 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '4s' }}
          ></div>
          <div
            className="absolute top-1/2 right-10 w-64 h-64 bg-yellow-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '6s' }}
          ></div>
        </div>

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-6 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <GlassCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-white mb-6">
                {t('doctorsPage.hero.title')}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                {t('doctorsPage.hero.subtitle')}
              </p>
            </GlassCard>
          </section>

          {/* Doctors Grid */}
          <section className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {doctorsData.map((doctor) => (
                <DoctorCard key={doctor.id} doctor={doctor} />
              ))}
            </div>
          </section>

          {/* Why Our Doctors Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-white text-center">
              Why Choose Our Medical Team?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🏆</div>
                  <h3 className="text-lg font-bold text-white">Board Certified</h3>
                  <p className="text-white/80 text-sm">
                    All our doctors are board-certified specialists
                  </p>
                </div>
              </GlassCard>
              
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">📚</div>
                  <h3 className="text-lg font-bold text-white">Continuous Learning</h3>
                  <p className="text-white/80 text-sm">
                    Regular training in latest techniques and technologies
                  </p>
                </div>
              </GlassCard>
              
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🌟</div>
                  <h3 className="text-lg font-bold text-white">Award Winners</h3>
                  <p className="text-white/80 text-sm">
                    Recognized for excellence in aesthetic medicine
                  </p>
                </div>
              </GlassCard>
              
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">❤️</div>
                  <h3 className="text-lg font-bold text-white">Patient-Centered</h3>
                  <p className="text-white/80 text-sm">
                    Personalized care for every patient's unique needs
                  </p>
                </div>
              </GlassCard>
            </div>
          </section>

          {/* Appointment CTA Section */}
          <section className="text-center">
            <GlassCard>
              <div className="max-w-2xl mx-auto space-y-6">
                <h2 className="text-3xl font-bold text-white">
                  {t('doctorsPage.appointment.title')}
                </h2>
                <p className="text-xl text-white/80">
                  Schedule a consultation with our expert medical team to discuss your beauty goals
                </p>
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0 px-8 py-4 text-lg"
                >
                  {t('doctorsPage.appointment.button')} <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </GlassCard>
          </section>

          {/* Clinic Standards Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-white text-center">
              Our Medical Standards
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🔬</div>
                  <h3 className="text-xl font-bold text-white">Evidence-Based Practice</h3>
                  <p className="text-white/80">
                    All treatments are based on scientific research and proven methodologies
                  </p>
                </div>
              </GlassCard>
              
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🛡️</div>
                  <h3 className="text-xl font-bold text-white">Safety First</h3>
                  <p className="text-white/80">
                    Comprehensive safety protocols and sterile environment for all procedures
                  </p>
                </div>
              </GlassCard>
              
              <GlassCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">✨</div>
                  <h3 className="text-xl font-bold text-white">Natural Results</h3>
                  <p className="text-white/80">
                    Focus on enhancing your natural beauty with subtle, long-lasting results
                  </p>
                </div>
              </GlassCard>
            </div>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Doctors;