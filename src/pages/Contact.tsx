import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { MapPin, Phone, Mail, Clock, Send } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Glass effect card component
const GlassCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      backdrop-blur-md bg-white/10 border border-white/20 shadow-xl
      ${hover ? 'hover:bg-white/15 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
      backdropFilter: 'blur(16px)',
      WebkitBackdropFilter: 'blur(16px)',
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

GlassCard.displayName = 'GlassCard';

const Contact = () => {
  const { t, currentLanguage } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSubmitting(false);
    // Reset form or show success message
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('contactPage.hero.title')} | Lullaby Clinic`}
        description={t('contactPage.hero.subtitle')}
        keywords="contact lullaby clinic, beauty clinic contact, appointment booking, clinic location"
      />
      
      <div
        className="min-h-screen relative"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-pink-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div
            className="absolute top-40 right-20 w-96 h-96 bg-purple-300/15 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '4s' }}
          ></div>
        </div>

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-6 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <GlassCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-white mb-6">
                {t('contactPage.hero.title')}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                {t('contactPage.hero.subtitle')}
              </p>
            </GlassCard>
          </section>

          {/* Contact Content */}
          <section className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <GlassCard>
              <h2 className="text-2xl font-bold text-white mb-6">
                {t('contactPage.form.title')}
              </h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-white/80 mb-2 font-medium">
                    {t('contactPage.form.name')}
                  </label>
                  <Input 
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="John Doe"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-white/80 mb-2 font-medium">
                    {t('contactPage.form.email')}
                  </label>
                  <Input 
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-white/80 mb-2 font-medium">
                    {t('contactPage.form.phone')}
                  </label>
                  <Input 
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleChange}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="+66 123 456 789"
                  />
                </div>
                
                <div>
                  <label className="block text-white/80 mb-2 font-medium">
                    {t('contactPage.form.subject')}
                  </label>
                  <Input 
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="Appointment Inquiry"
                  />
                </div>
                
                <div>
                  <label className="block text-white/80 mb-2 font-medium">
                    {t('contactPage.form.message')}
                  </label>
                  <Textarea 
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50 min-h-32"
                    placeholder="Tell us about your beauty goals..."
                    required
                  />
                </div>
                
                <Button 
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0"
                >
                  {isSubmitting ? (
                    <>Sending...</>
                  ) : (
                    <>
                      {t('contactPage.form.submit')} <Send className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              </form>
            </GlassCard>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* Contact Details */}
              <GlassCard>
                <h2 className="text-2xl font-bold text-white mb-6">Get In Touch</h2>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <MapPin className="w-6 h-6 text-pink-300 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-medium mb-1">
                        {t('contactPage.info.address.title')}
                      </h3>
                      <p className="text-white/80">
                        {t('contactPage.info.address.value')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Phone className="w-6 h-6 text-green-300 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-medium mb-1">
                        {t('contactPage.info.phone.title')}
                      </h3>
                      <p className="text-white/80">
                        {t('contactPage.info.phone.value')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Mail className="w-6 h-6 text-blue-300 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-medium mb-1">
                        {t('contactPage.info.email.title')}
                      </h3>
                      <p className="text-white/80">
                        {t('contactPage.info.email.value')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Clock className="w-6 h-6 text-yellow-300 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-medium mb-1">
                        {t('contactPage.info.hours.title')}
                      </h3>
                      <p className="text-white/80">
                        {t('contactPage.info.hours.value')}
                      </p>
                    </div>
                  </div>
                </div>
              </GlassCard>

              {/* Map */}
              <GlassCard>
                <h2 className="text-2xl font-bold text-white mb-6">
                  {t('contactPage.map.title')}
                </h2>
                <div className="bg-white/10 rounded-lg overflow-hidden">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3898.5856486772897!2d100.91735931475937!3d13.36254999072638!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTPCsDIxJzQ1LjIiTiAxMDDCsDU1JzE0LjQiRQ!5e0!3m2!1sen!2sth!4v1635234567890!5m2!1sen!2sth"
                    width="100%"
                    height="300"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Lullaby Clinic Location"
                  />
                </div>
              </GlassCard>
            </div>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Contact;