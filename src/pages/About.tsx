
import React from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useLanguage } from '@/contexts/LanguageContext';
import AboutHero from '@/components/about/AboutHero';
import MissionVision from '@/components/about/MissionVision';
import AboutApproach from '@/components/about/AboutApproach';
import DoctorsSection from '@/components/about/DoctorsSection';
import PartnersSection from '@/components/about/PartnersSection';
import BrandStatement from '@/components/about/BrandStatement';
import QuickLinksServices from '@/components/about/QuickLinksServices';
import AnimatedBackground from '@/components/about/AnimatedBackground';

const About = () => {
  const { t, currentLanguage } = useLanguage();

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('about.hero.title')} | Lullaby Clinic`}
        description={t('about.hero.subtitle')}
        keywords="about lullaby clinic, medical aesthetics, beauty clinic, professional doctors"
      />
      
      <div
        className="min-h-screen relative"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        <AnimatedBackground />
        <Navigation />

        <main className="container mx-auto px-6 pt-32 pb-12 space-y-16 relative z-10">
          <AboutHero />
          <MissionVision />
          <AboutApproach />
          <DoctorsSection />
          <PartnersSection />
          <BrandStatement />
          <QuickLinksServices />
        </main>

        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default About;
