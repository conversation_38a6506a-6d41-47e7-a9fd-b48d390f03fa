
import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Star, User, ThumbsUp, Quote } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Reviews data
const reviewsData = [
  {
    id: 1,
    name: '<PERSON>',
    service: 'facial_hydra',
    rating: 5,
    date: '2024-01-15',
    verified: true,
    beforeAfter: true
  },
  {
    id: 2,
    name: '<PERSON>',
    service: 'botox_forehead',
    rating: 5,
    date: '2024-01-10',
    verified: true,
    beforeAfter: false
  },
  {
    id: 3,
    name: '<PERSON>',
    service: 'filler_lips',
    rating: 4,
    date: '2024-01-08',
    verified: true,
    beforeAfter: true
  },
  {
    id: 4,
    name: 'Lisa Wang',
    service: 'laser_hair',
    rating: 5,
    date: '2024-01-05',
    verified: true,
    beforeAfter: false
  }
];

// Glass effect card component
const GlassCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      backdrop-blur-md bg-white/10 border border-white/20 shadow-xl
      ${hover ? 'hover:bg-white/15 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
      backdropFilter: 'blur(16px)',
      WebkitBackdropFilter: 'blur(16px)',
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

GlassCard.displayName = 'GlassCard';

// Star Rating Component
const StarRating = memo(({ rating, size = 'sm' }: { rating: number; size?: 'sm' | 'lg' }) => {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size === 'lg' ? 'w-6 h-6' : 'w-4 h-4'} ${
            star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-400'
          }`}
        />
      ))}
    </div>
  );
});

StarRating.displayName = 'StarRating';

// Review Card Component
const ReviewCard = memo(({ review }: { review: typeof reviewsData[0] }) => {
  const { t } = useLanguage();
  
  return (
    <GlassCard hover>
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-pink-400 to-purple-600 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-white" />
            </div>
            <div>
              <h4 className="font-semibold text-white">{review.name}</h4>
              <div className="flex items-center gap-2">
                <StarRating rating={review.rating} />
                {review.verified && (
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400 text-xs">
                    {t('reviews.verified')}
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <span className="text-white/60 text-sm">{review.date}</span>
        </div>
        
        <div className="relative">
          <Quote className="absolute top-0 left-0 w-6 h-6 text-white/30" />
          <p className="text-white/80 ml-8 leading-relaxed">
            {t(`reviews.testimonials.review_${review.id}.content`)}
          </p>
        </div>
        
        <div className="flex items-center justify-between">
          <Badge variant="outline" className="bg-white/10 text-white border-white/20">
            {t(`reviews.services.${review.service}`)}
          </Badge>
          {review.beforeAfter && (
            <div className="flex items-center gap-2 text-white/60 text-sm">
              <span>{t('reviews.hasBeforeAfter')}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2 pt-2">
          <Button variant="ghost" size="sm" className="text-white/70 hover:text-white hover:bg-white/10">
            <ThumbsUp className="w-4 h-4 mr-1" />
            {t('reviews.helpful')}
          </Button>
        </div>
      </div>
    </GlassCard>
  );
});

ReviewCard.displayName = 'ReviewCard';

// Review Form Component
const ReviewForm = memo(() => {
  const { t } = useLanguage();
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [formData, setFormData] = useState({
    name: '',
    service: '',
    title: '',
    content: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Review submission:', { rating, ...formData });
  };

  return (
    <GlassCard>
      <h3 className="text-2xl font-bold text-white mb-6">
        {t('reviews.writeReview')}
      </h3>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-white mb-2">
              {t('reviews.form.name')}
            </label>
            <Input
              placeholder={t('reviews.form.namePlaceholder')}
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
            />
          </div>
          
          <div>
            <label className="block text-white mb-2">
              {t('reviews.form.service')}
            </label>
            <Select value={formData.service} onValueChange={(value) => handleInputChange('service', value)}>
              <SelectTrigger className="bg-white/10 border-white/20 text-white">
                <SelectValue placeholder={t('reviews.form.selectService')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="facial_hydra">Hydrafacial</SelectItem>
                <SelectItem value="botox_forehead">Botox</SelectItem>
                <SelectItem value="filler_lips">Lip Filler</SelectItem>
                <SelectItem value="laser_hair">Laser Hair Removal</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div>
          <label className="block text-white mb-2">
            {t('reviews.form.rating')}
          </label>
          <div className="flex items-center gap-2">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onMouseEnter={() => setHoveredRating(star)}
                onMouseLeave={() => setHoveredRating(0)}
                onClick={() => setRating(star)}
                className="transition-colors"
              >
                <Star
                  className={`w-8 h-8 ${
                    star <= (hoveredRating || rating)
                      ? 'fill-yellow-400 text-yellow-400'
                      : 'text-gray-400 hover:text-yellow-200'
                  }`}
                />
              </button>
            ))}
            <span className="text-white ml-2">
              {rating > 0 && `${rating}/5`}
            </span>
          </div>
        </div>
        
        <div>
          <label className="block text-white mb-2">
            {t('reviews.form.title')}
          </label>
          <Input
            placeholder={t('reviews.form.titlePlaceholder')}
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
        </div>
        
        <div>
          <label className="block text-white mb-2">
            {t('reviews.form.content')}
          </label>
          <Textarea
            placeholder={t('reviews.form.contentPlaceholder')}
            value={formData.content}
            onChange={(e) => handleInputChange('content', e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
            rows={6}
          />
        </div>
        
        <Button 
          type="submit"
          className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0"
          disabled={!rating || !formData.name || !formData.content}
        >
          {t('reviews.form.submit')}
        </Button>
      </form>
    </GlassCard>
  );
});

ReviewForm.displayName = 'ReviewForm';

const Reviews = () => {
  const { t, currentLanguage } = useLanguage();
  const [filterRating, setFilterRating] = useState('all');

  const filteredReviews = reviewsData.filter(review => 
    filterRating === 'all' || review.rating.toString() === filterRating
  );

  // Calculate average rating
  const averageRating = reviewsData.reduce((sum, review) => sum + review.rating, 0) / reviewsData.length;

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('reviews.hero.title')} | Lullaby Clinic`}
        description={t('reviews.hero.subtitle')}
        keywords="lullaby clinic reviews, patient testimonials, beauty treatment reviews"
      />
      
      <div
        className="min-h-screen relative"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-pink-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div
            className="absolute top-40 right-20 w-96 h-96 bg-purple-300/15 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '4s' }}
          ></div>
        </div>

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-6 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <GlassCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-white mb-6">
                {t('reviews.hero.title')}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                {t('reviews.hero.subtitle')}
              </p>
              
              {/* Rating Summary */}
              <div className="flex flex-col md:flex-row items-center justify-center gap-8">
                <div className="text-center">
                  <div className="text-5xl font-bold text-white mb-2">
                    {averageRating.toFixed(1)}
                  </div>
                  <StarRating rating={Math.round(averageRating)} size="lg" />
                  <div className="text-white/70 mt-2">
                    {t('reviews.basedOn')} {reviewsData.length} {t('reviews.reviews')}
                  </div>
                </div>
                
                <div className="grid grid-cols-5 gap-4">
                  {[5, 4, 3, 2, 1].map((stars) => {
                    const count = reviewsData.filter(r => r.rating === stars).length;
                    const percentage = (count / reviewsData.length) * 100;
                    return (
                      <div key={stars} className="text-center">
                        <div className="text-white font-semibold">{stars}★</div>
                        <div className="w-4 h-20 bg-white/20 rounded-full mx-auto relative">
                          <div 
                            className="absolute bottom-0 w-full bg-yellow-400 rounded-full transition-all duration-500"
                            style={{ height: `${percentage}%` }}
                          />
                        </div>
                        <div className="text-white/60 text-sm mt-1">{count}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </GlassCard>
          </section>

          {/* Filter Section */}
          <section className="text-center">
            <GlassCard>
              <div className="flex items-center justify-center gap-4">
                <span className="text-white">{t('reviews.filterBy')}:</span>
                <Select value={filterRating} onValueChange={setFilterRating}>
                  <SelectTrigger className="w-40 bg-white/10 border-white/20 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('reviews.allRatings')}</SelectItem>
                    <SelectItem value="5">5 ⭐</SelectItem>
                    <SelectItem value="4">4 ⭐</SelectItem>
                    <SelectItem value="3">3 ⭐</SelectItem>
                    <SelectItem value="2">2 ⭐</SelectItem>
                    <SelectItem value="1">1 ⭐</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </GlassCard>
          </section>

          {/* Reviews Grid */}
          <section className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {filteredReviews.map((review) => (
              <ReviewCard key={review.id} review={review} />
            ))}
          </section>

          {/* Write Review Section */}
          <section>
            <ReviewForm />
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Reviews;
