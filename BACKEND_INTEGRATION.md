# Backend Integration Guide

This document provides comprehensive guidance for integrating the Lullaby Clinic frontend with the Payload CMS backend system.

## Overview

The Lullaby Clinic system uses a **hybrid architecture** that combines:

- **Supabase**: Primary database for user authentication, appointments, and real-time features
- **Payload CMS**: Content management system for doctors, services, blog posts, and media

The integration provides automatic failover, ensuring the frontend continues to work even if one backend system is unavailable.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React + Vite)                 │
├─────────────────────────────────────────────────────────────┤
│  Hybrid Service Layer                                      │
│  ├── Auto-failover logic                                   │
│  ├── Data transformation                                   │
│  └── Unified API interface                                 │
├─────────────────────────────────────────────────────────────┤
│  Primary: Payload CMS          │  Secondary: Supabase      │
│  ├── Content Management        │  ├── User Authentication  │
│  ├── Doctor Profiles          │  ├── Appointment Booking  │
│  ├── Service Catalog          │  ├── Real-time Updates    │
│  ├── Blog Posts               │  ├── Payment Processing   │
│  └── Media Management         │  └── User Profiles        │
└─────────────────────────────────────────────────────────────┘
```

## Quick Setup

### 1. Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Required - Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional - Payload CMS Backend
VITE_PAYLOAD_API_URL=http://localhost:3000
VITE_PAYLOAD_API_KEY=your_payload_api_key
VITE_BACKEND_GRAPHQL_URL=http://localhost:3000/api/graphql
VITE_BACKEND_REST_URL=http://localhost:3000/api
```

### 2. Start Backend Systems

**Payload CMS Backend:**
```bash
cd /path/to/lullaby-backend-payload/lullaby-clinic
npm run dev
```

**Frontend:**
```bash
npm run dev
```

### 3. Verify Integration

Visit `http://localhost:8080` and check the browser console for integration status.

## Usage Examples

### Basic Data Fetching

```tsx
import { useHybridDoctors, useHybridServices } from '@/hooks/useHybridContent';

function DoctorsList() {
  const { doctors, source, error } = useHybridDoctors({ 
    specialty: 'dermatology',
    isActive: true 
  });

  if (error) return <div>Error loading doctors</div>;

  return (
    <div>
      <p>Data source: {source}</p> {/* 'payload', 'supabase', or 'error' */}
      {doctors?.map(doctor => (
        <div key={doctor.id}>{doctor.first_name} {doctor.last_name}</div>
      ))}
    </div>
  );
}
```

### Authentication with Permissions

```tsx
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';

function AdminPanel() {
  const { 
    isAuthenticated, 
    role, 
    hasPermission, 
    canManageContent 
  } = useEnhancedAuth();

  if (!isAuthenticated) return <LoginForm />;
  if (!canManageContent()) return <AccessDenied />;

  return (
    <div>
      <h1>Admin Panel</h1>
      <p>Role: {role}</p>
      {hasPermission('manage_users') && <UserManagement />}
      {hasPermission('manage_content') && <ContentManagement />}
    </div>
  );
}
```

### Media Upload

```tsx
import MediaUpload from '@/components/MediaUpload';

function ProfileImageUpload() {
  return (
    <MediaUpload
      acceptedFileTypes={['image/*']}
      maxFileSize={5 * 1024 * 1024} // 5MB
      onUploadComplete={(media) => {
        console.log('Uploaded:', media);
        // Update user profile with media.url
      }}
      onUploadError={(error) => {
        console.error('Upload failed:', error);
      }}
    />
  );
}
```

### System Status Monitoring

```tsx
import { useBackendIntegrationStatus } from '@/hooks/useHybridContent';
import BackendStatus from '@/components/BackendStatus';

function SystemStatus() {
  const { isHealthy, statusMessage, systems } = useBackendIntegrationStatus();

  return (
    <div>
      <h2>System Status: {statusMessage}</h2>
      <BackendStatus showDetails={true} />
      
      {!systems.payload.available && (
        <Alert>Content management features may be limited</Alert>
      )}
    </div>
  );
}
```

## API Reference

### Hybrid Content Hooks

#### `useHybridDoctors(filters?)`
Fetches doctors from Payload CMS with Supabase fallback.

**Parameters:**
- `filters.specialty?: string` - Filter by medical specialty
- `filters.isActive?: boolean` - Filter by active status

**Returns:**
- `doctors: Doctor[]` - Array of doctor objects
- `source: 'payload' | 'supabase' | 'error'` - Data source used
- `error: string | null` - Error message if any

#### `useHybridServices(filters?)`
Fetches services from Payload CMS with Supabase fallback.

#### `useHybridBlogPosts(filters?)`
Fetches blog posts from Payload CMS with Supabase fallback.

### Authentication Hooks

#### `useEnhancedAuth()`
Provides integrated authentication with role-based permissions.

**Returns:**
- `isAuthenticated: boolean` - Authentication status
- `user: User | null` - Supabase user object
- `role: string | null` - User role from Payload CMS
- `permissions: string[]` - Array of user permissions
- `hasPermission(permission: string): boolean` - Check specific permission
- `canManageContent(): boolean` - Check content management access

### System Monitoring

#### `useBackendIntegrationStatus()`
Monitors the health of both backend systems.

#### `useSystemHealth()`
Detailed health check for all system components.

## Data Flow

### 1. User Authentication
```
User Login → Supabase Auth → Sync to Payload CMS → Enhanced Session
```

### 2. Content Fetching
```
Request → Try Payload CMS → Success? Return Data : Try Supabase → Return Data
```

### 3. Media Upload
```
File Upload → Payload CMS Media API → S3 Storage → Return Media URL
```

## Configuration Options

### Authentication Integration

```typescript
// src/lib/auth-integration.ts
export const AUTH_INTEGRATION_CONFIG = {
  SYNC_USERS: true,                    // Sync users between systems
  AUTO_CREATE_PAYLOAD_USERS: true,    // Auto-create Payload users
  DEFAULT_PAYLOAD_ROLE: 'patient',    // Default role for new users
  SYNC_PROFILE_UPDATES: true,         // Sync profile changes
};
```

### API Client Configuration

```typescript
// src/lib/payload-api.ts
export const PAYLOAD_CONFIG = {
  API_URL: 'http://localhost:3000',    // Payload CMS URL
  TIMEOUT: 10000,                      // Request timeout (ms)
  RETRIES: 3,                          // Number of retries
};
```

## Troubleshooting

### Common Issues

#### 1. "Payload CMS not available" Error
**Cause:** Backend server not running or incorrect URL
**Solution:**
```bash
# Check if backend is running
curl http://localhost:3000/health

# Verify environment variables
echo $VITE_PAYLOAD_API_URL
```

#### 2. Authentication Sync Issues
**Cause:** User not syncing between Supabase and Payload
**Solution:**
```typescript
// Manually trigger sync
import { authIntegration } from '@/lib/auth-integration';
await authIntegration.syncUserToPayload(user, profile);
```

#### 3. Media Upload Failures
**Cause:** S3 configuration or file size limits
**Solution:**
- Check S3 credentials in Payload CMS
- Verify file size limits
- Check CORS configuration

### Debug Tools

#### Development Test Panel
Add to any component in development:
```tsx
import IntegrationTestPanel from '@/components/IntegrationTestPanel';

// Only shows in development mode
<IntegrationTestPanel />
```

#### Console Debugging
```typescript
// Enable verbose logging
localStorage.setItem('debug', 'payload:*');

// Check data sources
import { useDataSourceDebug } from '@/hooks/useHybridContent';
const debug = useDataSourceDebug();
console.log('Data sources:', debug.dataSources);
```

## Performance Considerations

### Caching Strategy
- **Payload CMS data**: 10-15 minutes cache
- **Supabase data**: 2-5 minutes cache
- **Authentication**: 5 minutes cache

### Optimization Tips
1. Use `select` to limit fields in API calls
2. Implement pagination for large datasets
3. Use React Query's background refetching
4. Monitor bundle size with hybrid services

## Security

### API Key Management
- Never expose Payload API keys in client code
- Use environment variables for all credentials
- Implement proper CORS settings

### Permission System
```typescript
// Role-based permissions
const ROLE_PERMISSIONS = {
  admin: ['manage_users', 'manage_content', 'view_analytics'],
  doctor: ['view_appointments', 'manage_medical_records'],
  receptionist: ['manage_appointments', 'view_patients'],
  patient: ['view_own_appointments', 'update_own_profile'],
};
```

## Migration Guide

### From Supabase-only to Hybrid
1. Install new dependencies
2. Update environment variables
3. Replace service hooks with hybrid versions
4. Test fallback behavior
5. Deploy with feature flags

### Rollback Plan
The hybrid system maintains full backward compatibility. To rollback:
1. Remove Payload CMS environment variables
2. System automatically uses Supabase for all operations

## Support

For issues or questions:
1. Check the troubleshooting section
2. Use the development test panel
3. Review browser console logs
4. Check both backend system logs
