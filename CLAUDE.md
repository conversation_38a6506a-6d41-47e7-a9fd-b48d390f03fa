# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands


##  Development Guidelines


### System Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Lullaby Clinic System                    │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React + Supabase)                               │
│  Location: /lullaby-homepage-checks                        │
│  ├── User Interface (Patients, Staff)                      │
│  ├── Admin Dashboard                                        │
│  ├── Appointment Management                                 │
│  └── Authentication (Supabase)                             │
├─────────────────────────────────────────────────────────────┤
│  Backend (Payload CMS) - THIS PROJECT                      │
│  Location: /lullaby-backend-payload/lullaby-clinic         │
│  ├── Content Management System                             │
│  ├── Media Management                                       │
│  ├── User Management                                        │
│  ├── GraphQL/REST APIs                                     │
│  └── PostgreSQL Database                                   │
└─────────────────────────────────────────────────────────────┘
```

**Start Development Server:**
```bash
npm run dev
```
- Runs on port 8080 by default (configured in vite.config.ts)

**Build & Preview:**
```bash
npm run build        # Production build
npm run build:dev    # Development build with sourcemaps
npm run preview      # Preview production build
```

**Code Quality:**
```bash
npm run lint         # ESLint check
```

**Project Exploration:**
```bash
npm run explore                # Full project exploration
npm run explore:structure      # View project structure
npm run explore:components     # List all components
npm run project:tree          # Show TypeScript file tree
npm run status                 # View current project status
```

## Architecture Overview

**Tech Stack:**
- React 18 + TypeScript + Vite
- UI: shadcn/ui components + Radix UI primitives + Tailwind CSS
- Routing: React Router DOM with i18n support
- State: React Query for server state, React Context for global state
- Styling: Tailwind CSS with custom configuration
- Development: ESLint + TypeScript strict mode

**Multi-language Architecture:**
- Supported languages: Thai (th), English (en), Chinese (zh)
- Language detection: URL params → localStorage → browser → default (en)
- Translation system: `src/utils/translations.ts` with nested key structure
- Font switching: Kanit for Thai, Poppins for others
- Language context: `src/contexts/LanguageContext.tsx` provides `t()` function
- URL structure: `/:lang` routes for language switching

**Component Structure:**
- `src/components/ui/`: shadcn/ui base components (buttons, dialogs, etc.)
- `src/components/`: Business logic components (Navigation, Footer, etc.)
- `src/pages/`: Route-level components
- `src/hooks/`: Custom React hooks
- All components use TypeScript with proper typing

**Key Design Patterns:**
- Error boundaries at multiple levels (critical, page)
- Lazy loading with performance optimizations
- Responsive design mobile-first approach
- SEO optimization with react-helmet-async
- Performance monitoring with custom hooks

**Build Optimization:**
- Manual chunk splitting for better caching (vendor, ui, utils, etc.)
- Terser minification with console removal in production
- CSS minification and source map generation in development
- Bundle size warnings at 1000kb threshold


## Enviromentent Variables
Create a `.env` file in the root directory and add the following variables:
```bash
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```


## Code Style Requirements

Based on .cursorrules file:
- 2 space indentation
- Max 100 characters per line
- Single quotes for strings
- Always use semicolons
- PascalCase for components (matching filename)
- camelCase for functions and variables
- Use `@/` alias for imports (configured in vite.config.ts and tsconfig.json)
- Tailwind utilities first, conditionals second in className
- TypeScript strict mode: no `any`, use proper typing
- Use strict mode for tpye safty
- Define types for all props and state
- Catch all errors, return typed objects or Result pattern
- Testing: Vitest → React Testing Library → Playwright
- Commits: use Conventional Commit style (e.g., `feat:`, `fix:`)

## Translation Usage

Use the `useLanguage` hook to access translations:
```tsx
const { t, currentLanguage, changeLanguage } = useLanguage();
const text = t('nav.home'); // Returns "Home", "หน้าแรก", or "首页"
```

Translation keys follow dot notation (e.g., `hero.title`, `services.facial.description`).

## Performance Considerations

- Images should use the LazyImage component for performance
- Performance monitoring available via usePerformance hook
- Build optimizations configured for production deployment
- Component lazy loading and code splitting implemented

## State Management

**State Management Strategy:**
- **React Context**: Use for simple global state (theme, language, user preferences)
- **React Query**: Use for server state management (API calls, caching, synchronization)
- **Component State**: Keep state local when possible (forms, UI toggles, temporary data)
- **Avoid Prop Drilling**: Leverage state management tools and composition patterns

**Current Implementation:**
- `LanguageContext`: Global language state and translation functions
- React Query: Server state for API calls (when implemented)
- Local useState/useReducer: Component-specific state

**Best Practices:**
- **Server State vs Client State**: Clearly separate server data (React Query) from client-only data (Context/useState)
- **State Colocation**: Keep state as close to where it's used as possible
- **Derived State**: Compute derived values in render rather than storing them
- **State Normalization**: For complex data structures, consider normalizing state shape
- **Performance**: Use React.memo, useMemo, and useCallback judiciously for expensive operations

**When to Use Each:**

*React Context:*
- User authentication status
- Theme/dark mode preferences
- Language/locale settings
- Global UI state (modals, notifications)

*React Query:*
- API data fetching and caching
- Server synchronization
- Background updates
- Optimistic updates

*Local Component State:*
- Form inputs and validation
- UI component state (expanded/collapsed)
- Temporary data that doesn't need persistence
- Component-specific toggles and flags

**Anti-patterns to Avoid:**
- Storing server data in React Context
- Excessive prop drilling (more than 2-3 levels)
- Putting all state in global context
- Using Context for frequently changing data without optimization

#### Data Flow
```
React Frontend (Supabase Auth) ←→ Payload CMS Backend (Content & Media)
                ↓                              ↓
        User Authentication              Content Management
        Appointment Data                 Media Storage
        Dashboard Analytics             User Profiles
```

#### Dual Authentication Strategy
- **Frontend**: Supabase authentication for user sessions
- **Backend**: Payload authentication for admin/content management
- **Integration**: Potential user synchronization between systems

## Database Schema

This backend uses PostgreSQL with Drizzle ORM via Payload CMS. The schema is designed for a comprehensive clinic management system.

### Core Collections

#### Users Collection
```typescript
{
  slug: 'users',
  auth: true,
  fields: [
    { name: 'email', type: 'email', required: true, unique: true },
    { name: 'role', type: 'select', options: ['admin', 'doctor', 'receptionist', 'patient'], required: true },
    { name: 'firstName', type: 'text', required: true },
    { name: 'lastName', type: 'text', required: true },
    { name: 'phone', type: 'text', validate: phoneValidation },
    { name: 'isActive', type: 'checkbox', defaultValue: true }
  ]
}
```

#### Patients Collection
```typescript
{
  slug: 'patients',
  fields: [
    { name: 'user', type: 'relationship', relationTo: 'users', required: true },
    { name: 'dateOfBirth', type: 'date', required: true },
    { name: 'gender', type: 'select', options: ['male', 'female', 'other'] },
    { name: 'address', type: 'textarea' },
    { name: 'emergencyContact', type: 'group', fields: [
      { name: 'name', type: 'text' },
      { name: 'phone', type: 'text' },
      { name: 'relationship', type: 'text' }
    ]},
    { name: 'allergies', type: 'textarea' },
    { name: 'medicalHistory', type: 'richText' },
    { name: 'insuranceInfo', type: 'group', fields: [
      { name: 'provider', type: 'text' },
      { name: 'policyNumber', type: 'text' },
      { name: 'groupNumber', type: 'text' }
    ]}
  ]
}
```

#### Doctors Collection
```typescript
{
  slug: 'doctors',
  fields: [
    { name: 'user', type: 'relationship', relationTo: 'users', required: true },
    { name: 'licenseNumber', type: 'text', required: true, unique: true },
    { name: 'specializations', type: 'array', fields: [
      { name: 'specialty', type: 'text' }
    ]},
    { name: 'qualifications', type: 'richText' },
    { name: 'experience', type: 'number' },
    { name: 'consultationFee', type: 'number' },
    { name: 'workingHours', type: 'group', fields: [
      { name: 'monday', type: 'group', fields: [
        { name: 'start', type: 'text' },
        { name: 'end', type: 'text' },
        { name: 'isWorking', type: 'checkbox' }
      ]},
      // Repeat for other days
    ]},
    { name: 'profileImage', type: 'relationship', relationTo: 'media' }
  ]
}
```

#### Appointments Collection
```typescript
{
  slug: 'appointments',
  fields: [
    { name: 'patient', type: 'relationship', relationTo: 'patients', required: true },
    { name: 'doctor', type: 'relationship', relationTo: 'doctors', required: true },
    { name: 'service', type: 'relationship', relationTo: 'services', required: true },
    { name: 'appointmentDate', type: 'date', required: true },
    { name: 'appointmentTime', type: 'text', required: true },
    { name: 'duration', type: 'number', defaultValue: 30 },
    { name: 'status', type: 'select', options: [
      'scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled', 'no-show'
    ], defaultValue: 'scheduled' },
    { name: 'notes', type: 'textarea' },
    { name: 'symptoms', type: 'textarea' },
    { name: 'diagnosis', type: 'richText' },
    { name: 'prescription', type: 'richText' },
    { name: 'followUpRequired', type: 'checkbox' },
    { name: 'followUpDate', type: 'date' },
    { name: 'totalAmount', type: 'number' },
    { name: 'paymentStatus', type: 'select', options: ['pending', 'paid', 'partial', 'refunded'] }
  ]
}
```

#### Services Collection
```typescript
{
  slug: 'services',
  fields: [
    { name: 'name', type: 'text', required: true },
    { name: 'description', type: 'richText' },
    { name: 'category', type: 'select', options: [
      'consultation', 'diagnostic', 'treatment', 'surgery', 'therapy', 'preventive'
    ]},
    { name: 'duration', type: 'number', required: true },
    { name: 'price', type: 'number', required: true },
    { name: 'isActive', type: 'checkbox', defaultValue: true },
    { name: 'requiresPreparation', type: 'checkbox' },
    { name: 'preparationInstructions', type: 'richText' },
    { name: 'availableDoctors', type: 'relationship', relationTo: 'doctors', hasMany: true }
  ]
}
```

#### Medical Records Collection
```typescript
{
  slug: 'medical-records',
  access: {
    read: ({ req }) => doctorOrPatientAccess(req),
    create: ({ req }) => doctorAccess(req),
    update: ({ req }) => doctorAccess(req)
  },
  fields: [
    { name: 'patient', type: 'relationship', relationTo: 'patients', required: true },
    { name: 'doctor', type: 'relationship', relationTo: 'doctors', required: true },
    { name: 'appointment', type: 'relationship', relationTo: 'appointments' },
    { name: 'recordType', type: 'select', options: [
      'consultation', 'lab-result', 'imaging', 'prescription', 'surgery', 'discharge'
    ]},
    { name: 'title', type: 'text', required: true },
    { name: 'content', type: 'richText', required: true },
    { name: 'attachments', type: 'relationship', relationTo: 'media', hasMany: true },
    { name: 'isConfidential', type: 'checkbox' },
    { name: 'shareWithPatient', type: 'checkbox', defaultValue: true }
  ]
}
```

### Database Relationships

```
Users (1) ←→ (1) Patients
Users (1) ←→ (1) Doctors
Patients (1) ←→ (∞) Appointments
Doctors (1) ←→ (∞) Appointments
Services (1) ←→ (∞) Appointments
Patients (1) ←→ (∞) Medical Records
Doctors (1) ←→ (∞) Medical Records
Appointments (1) ←→ (0..1) Medical Records
Media (1) ←→ (∞) Medical Records (attachments)
```

### Field Validation Rules

**Email Validation:**
```typescript
const emailValidation = (val: string) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Invalid email format'
}
```

**Phone Validation:**
```typescript
const phoneValidation = (val: string) => {
  return /^[\+]?[1-9][\d]{0,15}$/.test(val) || 'Invalid phone number'
}
```

**Date Validation:**
```typescript
const futureDateValidation = (val: string) => {
  return new Date(val) > new Date() || 'Date must be in the future'
}
```

### Access Control & Permissions

**Role-Based Access:**
```typescript
const adminAccess = ({ req }) => req.user?.role === 'admin'
const doctorAccess = ({ req }) => ['admin', 'doctor'].includes(req.user?.role)
const receptionistAccess = ({ req }) => ['admin', 'doctor', 'receptionist'].includes(req.user?.role)
const patientAccess = ({ req }) => req.user?.role === 'patient'

const doctorOrPatientAccess = ({ req }) => {
  if (req.user?.role === 'admin' || req.user?.role === 'doctor') return true
  if (req.user?.role === 'patient') {
    return { patient: { equals: req.user.patient?.id } }
  }
  return false
}
```

**Data Privacy Considerations:**
- Medical records have restricted access based on user roles
- Patients can only view their own records
- Doctors can view records for their patients
- Confidential records have additional access restrictions
- Audit logging for sensitive data access

## Testing Strategy

### Testing Framework Setup

**Dependencies:**
```bash
pnpm add -D jest ts-jest @types/jest supertest @types/supertest
pnpm add -D testcontainers @testcontainers/postgresql
pnpm add -D @payloadcms/testing-utils
```

**Jest Configuration (jest.config.ts):**
```typescript
export default {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/payload-types.ts'
  ],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts']
}
```

### Test Categories

#### 1. Unit Tests
**Location:** `tests/unit/`

**Collections Tests:**
```typescript
// tests/unit/collections/patients.test.ts
describe('Patients Collection', () => {
  test('should validate required fields', () => {
    // Test field validation
  })
  
  test('should enforce unique constraints', () => {
    // Test uniqueness validation
  })
})
```

**Utilities Tests:**
```typescript
// tests/unit/utils/validation.test.ts
describe('Validation Utils', () => {
  test('should validate email format', () => {
    expect(emailValidation('<EMAIL>')).toBe(true)
    expect(emailValidation('invalid-email')).toContain('Invalid email')
  })
})
```

#### 2. Integration Tests
**Location:** `tests/integration/`

**API Endpoint Tests:**
```typescript
// tests/integration/appointments.test.ts
describe('Appointments API', () => {
  beforeAll(async () => {
    // Setup test database
    await setupTestDB()
  })
  
  test('should create appointment with valid data', async () => {
    const response = await request(app)
      .post('/api/appointments')
      .send(validAppointmentData)
      .expect(201)
  })
  
  test('should enforce doctor availability', async () => {
    // Test scheduling conflicts
  })
})
```

#### 3. Database Tests
**Test Database Setup:**
```typescript
// tests/setup.ts
import { PostgreSqlContainer } from '@testcontainers/postgresql'

let container: PostgreSqlContainer

beforeAll(async () => {
  container = await new PostgreSqlContainer()
    .withDatabase('test_lullaby_clinic')
    .withUsername('test')
    .withPassword('test')
    .start()
    
  process.env.DATABASE_URI = container.getConnectionUri()
})

afterAll(async () => {
  await container.stop()
})
```

#### 4. Access Control Tests
```typescript
// tests/integration/access-control.test.ts
describe('Access Control', () => {
  test('patients can only access their own records', async () => {
    const patientUser = await createTestUser('patient')
    const response = await authenticatedRequest(patientUser)
      .get('/api/medical-records')
      .expect(200)
    
    expect(response.body.docs).toHaveLength(1)
  })
  
  test('doctors can access all patient records', async () => {
    const doctorUser = await createTestUser('doctor')
    // Test doctor access
  })
})
```

### Test Scripts
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:integration": "jest --testPathPattern=integration",
    "test:unit": "jest --testPathPattern=unit"
  }
}
```

## Troubleshooting

### Common Errors

| Error | Cause | Solution |
|-------|-------|----------|
| `Database connection failed` | Incorrect `DATABASE_URI` or DB not running | Verify `.env` file and ensure PostgreSQL is running |
| `S3 upload permission denied` | Invalid AWS credentials or bucket policy | Check `S3_ACCESS_KEY_ID`, `S3_SECRET_ACCESS_KEY`, and bucket CORS |
| `Payload admin won't load` | Missing `PAYLOAD_SECRET` or build issues | Set `PAYLOAD_SECRET` in `.env` and run `pnpm build` |
| `Migration failed` | Schema conflicts or missing migrations | Run `pnpm payload migrate` or check migration files |
| `Authentication errors` | JWT token issues or user role problems | Clear browser storage and check user roles in database |
| `File upload fails` | Media collection misconfigured | Verify S3 bucket settings and upload field configuration |
| `GraphQL errors` | Type generation out of sync | Run `pnpm payload generate:types` |
| `Access denied on medical records` | Incorrect role-based access rules | Check access control functions and user roles |

### Environment Variables Checklist

**Required Variables:**
```bash
# Database
DATABASE_URI=postgresql://user:password@localhost:5432/lullaby_clinic

# Payload
PAYLOAD_SECRET=your-secret-key-here
PAYLOAD_CONFIG_PATH=src/payload.config.ts

# S3 Storage
S3_BUCKET=your-bucket-name
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key
S3_REGION=your-region
S3_ENDPOINT=https://s3.your-region.amazonaws.com

# Optional
NODE_ENV=development
PORT=3000
```

### Debugging Commands

**Enable Verbose Logging:**
```bash
export DEBUG="payload:*"
pnpm dev
```

**Database Debugging:**
```bash
# Check database connection
pnpm payload migrate:status

# Reset database (CAUTION: Development only)
pnpm payload migrate:reset

# Generate fresh types
pnpm payload generate:types
```

**Development Server Issues:**
```bash
# Clear Next.js cache
rm -rf .next

# Safe development restart
pnpm devsafe

# Check for TypeScript errors
pnpm tsc --noEmit
```

**S3 Storage Debugging:**
```bash
# Test S3 connection (requires AWS CLI)
aws s3 ls s3://your-bucket-name --region your-region

# Check bucket permissions
aws s3api get-bucket-policy --bucket your-bucket-name
```

### Performance Optimization

**Database Indexing:**
```sql
-- Add indexes for frequently queried fields
CREATE INDEX idx_appointments_date ON appointments(appointment_date);
CREATE INDEX idx_appointments_doctor ON appointments(doctor_id);
CREATE INDEX idx_appointments_patient ON appointments(patient_id);
CREATE INDEX idx_medical_records_patient ON medical_records(patient_id);
```

**Query Optimization:**
```typescript
// Use select to limit fields
const appointments = await payload.find({
  collection: 'appointments',
  select: {
    appointmentDate: true,
    status: true,
    patient: true
  }
})

// Use pagination for large datasets
const patients = await payload.find({
  collection: 'patients',
  limit: 20,
  page: 1
})
```

### Security Best Practices

**Data Sanitization:**
```typescript
// Sanitize user input
const sanitizeInput = (input: string) => {
  return input.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
}
```

**Rate Limiting:**
```typescript
// Add rate limiting to sensitive endpoints
import rateLimit from 'express-rate-limit'

const appointmentLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10 // limit each IP to 10 requests per windowMs
})
```

**Audit Logging:**
```typescript
// Log sensitive operations
const auditLog = {
  user: req.user.id,
  action: 'VIEW_MEDICAL_RECORD',
  resource: recordId,
  timestamp: new Date(),
  ip: req.ip
}
```
